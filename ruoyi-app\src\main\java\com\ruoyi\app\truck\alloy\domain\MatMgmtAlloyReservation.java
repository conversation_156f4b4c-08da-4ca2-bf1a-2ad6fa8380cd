package com.ruoyi.app.truck.alloy.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 物管合金车辆预约对象 mat_mgmt_alloy_reservationreservationNo
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class MatMgmtAlloyReservation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 预约编号 */
    @Excel(name = "预约编号")
    private String reservationNo;

    /** 状态 1-待审核 2-待签到 3-物管分配 4-待入厂 5-已入厂 6-物管确认 7-已出厂 21-驳回 22-待取消 23-已取消  */
    @Excel(name = "状态", readConverterExp = "1=待审核,2=待签到,3=物管分配,4=待入厂,5=已入厂,6=物管确认,7=已出厂,21=驳回,22=待取消,23=已取消")
    private String status;

    /** 供方业务员姓名 */
    @Excel(name = "供方业务员姓名")
    private String supplierSalesName;

    /** 供方业务员手机号 */
    @Excel(name = "供方业务员手机号")
    private String supplierSalesPhone;

    /** 申请单位编号 */
//    @Excel(name = "申请单位编号")
    private String applyCompanyId;

    /** 申请单位名称 */
    @Excel(name = "申请单位")
    private String applyCompanyName;

    /** 合金类型（A或B） */
    @Excel(name = "合金类型")
    private String alloyType;

    /** 合金字典值 */
//    @Excel(name = "合金字典值")
    private String alloyValue;

    /** 合金字典名称 */
    @Excel(name = "合金")
    private String alloyLabel;

    /** 合金吨数 */
    @Excel(name = "合金吨数")
    private BigDecimal estimatedWeight;

    /** 预计送货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计送货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expectedDeliveryTime;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkinTime;

    /** 进厂大门 1-A门 2-B门 */
    @Excel(name = "进厂大门", readConverterExp = "1=安全村,2=三号门")
    private String enterDoor;

    /** 有效开始时间（分配时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveStartTime;

    /** 有效结束时间（当天结束时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "有效结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveEndTime;

    /** 实际入厂时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "实际入厂时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualEarliestTime;

    /** 实际离厂时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "实际离厂时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualLatestTime;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNo;

    /** 车牌颜色 1-蓝 2-绿 3-黄 4-黄绿 */
    @Excel(name = "车牌颜色", readConverterExp = "1=蓝,2=绿,3=黄,4=黄绿")
    private String licensePlateColor;

    /** 车辆排放标准 1-国五 2-国六 3-新能源 */
    @Excel(name = "车辆排放标准", readConverterExp = "1=国五,2=国六,3=新能源")
    private Integer vehicleEmissionStandards;

    /** 操作人openid */
//    @Excel(name = "操作人openid")
    private String operatorOpenid;

    /** 司机名 */
    @Excel(name = "司机")
    private String driverName;

    /** 司机身份证 */
    @Excel(name = "司机身份证")
    private String driverCardNo;

    /** 司机联系电话 */
    @Excel(name = "司机联系电话")
    private String driverMobile;

    /** 司机人脸照片 */
//    @Excel(name = "司机人脸照片")
    private String driverFaceImg;

    /** 驾驶证照片 逗号分隔 */
//    @Excel(name = "驾驶证照片")
    private String driverLicenseImgs;

    /** 采购中心审核人 */
//    @Excel(name = "采购中心审核人")
    private String businessApprover;

    /** 行驶证照片 逗号分隔 */
//    @Excel(name = "行驶证照片")
    private String drivingLicenseImg;

    /** 采购中心审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "采购中心审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date businessApproveTime;

    /** 采购中心审核意见 */
//    @Excel(name = "采购中心审核意见")
    private String businessApproveReason;

    /** 毛重 */
//    @Excel(name = "毛重")
    private BigDecimal gross;

    /** 皮重 */
//    @Excel(name = "皮重")
    private BigDecimal tare;

    /** 净重 */
//    @Excel(name = "净重")
    private BigDecimal netWeight;

    /** 毛重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "毛重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date grossTime;

    /** 皮重时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "皮重时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tareTime;

    /** 删除标记 0-未删除 1-已删除 */
    private String delFlag;

    /** 推送编号 */
    private String pushNo;

    /** 查询开始时间 预约单列表使用 */
    private String queryStartTime;

    /** 查询结束时间 预约单列表使用 */
    private String queryEndTime;

    /** 状态列表 */
    private List<String> reservationStatusList;

    /** 操作日志列表 */
    private List<MatMgmtAlloyLog> matMgmtAlloyLogs;
}
