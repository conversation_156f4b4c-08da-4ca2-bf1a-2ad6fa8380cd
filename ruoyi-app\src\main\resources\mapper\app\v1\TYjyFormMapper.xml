<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.TYjyFormMapper">
    
    <resultMap type="TYjyForm" id="TYjyFormResult">
        <result property="id"    column="id"    />
        <result property="answerId"    column="answer_id"    />
        <result property="dimensionalityId"    column="dimensionality_id"    />
        <result property="dimensionalityPath"    column="dimensionality_path"    />
        <result property="formQuestion"    column="form_question"    />
        <result property="frequency"    column="frequency"    />
        <result property="formType"    column="form_type"    />
        <result property="creatorDeptName"    column="creator_dept_name"    />
        <result property="creatorDeptCode"    column="creator_dept_code"    />
        <result property="checkerList"    column="checker_list"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="deptCode"    column="dept_code"    />
        <result property="distributeDept"    column="distribute_dept"    />
        <result property="formNote"    column="form_note"    />
        <result property="formNote1"    column="form_note1"    />
        <result property="noteDept"    column="note_dept"    />
        <result property="maximum"    column="maximum"    />
        <result property="minimum"    column="minimum"    />
        <result property="unit"    column="unit"    />
        <result property="status"    column="status"    />
        <result property="formValue"    column="form_value"    />
        <result property="formFile"    column="form_file"    />
        <result property="creatorNo"    column="creator_no"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="deadlineSwitch"    column="deadline_switch"    />
        <result property="deadlineDate"    column="deadline_date"    />
        <result property="mailSwitch"    column="mail_switch"    />
        <result property="mailDate"    column="mail_date"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="countdown"    column="countdown"    />
        <result property="checkWorkNo"    column="check_work_no"    />
        <result property="checkUserName"    column="check_user_name"    />
        <result property="reason"    column="reason"    />
        <result property="measure"    column="measure"    />
        <result property="assessment"    column="assessment"   />
        <result property="deptShow"    column="dept_show"   />
        <result property="dimensionalityNameExport"    column="dimensionality_name_export"   />
        <result property="formQuestionExport"    column="form_question_export"   />
        <result property="formQuestionSort"    column="form_question_sort"   />
        <result property="mailNum"    column="mail_num"   />
        <result property="mailTime"    column="mail_time"   />

        <result property="version"    column="version"    />
        <result property="checkHistory"    column="check_history"    />


        <result property="fcDate"    column="fc_date"    />
        <result property="connectType"    column="connect_type"    />
        <result property="connectId"    column="connect_id"    />

        <result property="ruleType"    column="rule_type"    />

    </resultMap>



    <resultMap type="Integer" id="countResult">
        <result property="count"    column="count"    />
    </resultMap>

    <sql id="selectTYjyFormVo">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown,t.dept_show,t.connect_type,t.connect_id
        from t_yjy_form t
    </sql>

    <select id="selectTYjyFormList" parameterType="TYjyForm" resultMap="TYjyFormResult">
        <include refid="selectTYjyFormVo"/>
        <where>
            del_flag='0'
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
<!--            <if test="deptCode != null  and deptCode != ''"> and t.dept_code = #{deptCode}</if>-->
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>



    <select id="selectTYjyFormListWithFormQuestion" parameterType="TYjyForm" resultMap="TYjyFormResult">
        <include refid="selectTYjyFormVo"/>
        <where>
            del_flag='0'
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question = #{formQuestion}</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
            <!--            <if test="deptCode != null  and deptCode != ''"> and t.dept_code = #{deptCode}</if>-->
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>


    <select id="selectTYjyFormListWithDimensionalityPlus" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown
        from t_yjy_form t
        <where>
            del_flag='0'
            <foreach item="item" collection="dimensionalityIds" open="and (" separator=" or " close=")">
                t.dimensionality_path like concat(CONVERT(#{item},CHAR),',%')
            </foreach>
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
<!--            <if test="deptCode != null  and deptCode != ''"> and t.dept_code = #{deptCode}</if>-->
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

<!--    获取某个维度下某个时间某个评率的问题填报概况-->
<!--    <select id="selectFormListForAdmin" parameterType="TYjyForm" resultMap="TYjyFormResult">-->
<!--        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,j.status status,j.form_value,j.creator_no,j.creator_name-->
<!--        from t_yjy_form t left join (select * from t_yjy_answer where fc_date=#{fcDate} and del_flag='0') j on t.id=j.form_id-->
<!--        <where>-->
<!--            t.del_flag='0'-->
<!--            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>-->
<!--            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>-->
<!--            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>-->
<!--            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>-->
<!--            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>-->
<!--            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>-->
<!--            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>-->
<!--        </where>-->
<!--        order by t.dimensionality_path,t.sort_num,t.id-->
<!--    </select>-->

    <select id="selectFormListForAdmin" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.creator_no,j.creator_name,j.reason,j.measure,j.assessment
        from t_yjy_form t left join
        (
            select * from t_yjy_answer
            where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0' and t.count_flag='0'
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>


    <select id="selectFormListForAdminNoCount" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.form_question_sort,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.creator_no,j.creator_name,j.reason,j.measure,j.assessment
        from t_yjy_form t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0' and ( j.id is null or j.del_flag='0')
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <select id="selectFormListUnadmin" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.check_work_no,j.check_user_name,IFNULL(k.mail_num,0) as mail_num,IFNULL(k.mail_time,'') as mail_time
        from t_yjy_form t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        left join
        (
            select form_id,count(1) as mail_num ,max(DATE_FORMAT(mial_time, '%Y年%m月%d日 %H时')) as mail_time from t_yjy_mail_history
            where 1=1 and
            (
            (frequency='0' and fc_date=#{fcDate0})
            or (frequency='1' and fc_date=#{fcDate1} )
            or (frequency='2' and fc_date=#{fcDate2} )
            or (frequency='3' and fc_date=#{fcDate3} )
            or (frequency='4' and fc_date=#{fcDate4} )
            or (frequency='5' and fc_date=#{fcDate5} )
            )
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            group by form_id
        ) k on t.id=k.form_id
        <where>
            t.del_flag='0'  and IFNULL(j.status,'4') !='2'
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
        </where>

        order by t.dimensionality_path,t.sort_num,t.id
    </select>


    <select id="selectFormListUnsubmit" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,IFNULL(j.status,'4') status,j.check_work_no,j.check_user_name
        from
        (
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.sort_num
        from t_yjy_form t left join t_yjy_permission i on t.id=i.form_id
        where  i.work_no=#{workNo} and i.rule_type='99'
        )t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0' and IFNULL(j.status,'4') !='2'
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <!-- 没考虑到status,还有进一步优化的空间 -->
    <select id="selectFormListForAdminNum" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select ISNULL(j.id) as id ,concat(count(0),'') as form_value
        from t_yjy_form t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0'
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        group by ISNULL(j.id)
    </select>



    <select id="selectFormListForSubmit" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id,t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.form_question_sort,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown,t.dept_show,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.creator_no,j.creator_name,j.assessment,j.check_work_no,j.check_user_name,j.id as answer_id,j.version,j.check_history,j.reason,j.measure,t.rule_type
        from
        (
            select t.*,i.rule_type
            from t_yjy_form t left join t_yjy_permission i on t.id=i.form_id
            where  i.work_no=#{workNo}
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <!--where  i.work_no=#{workNo} and i.rule_type='99'-->
        )t left join
        (
            select * from t_yjy_answer
            where del_flag='0' and frequency='0' and fc_date=#{fcDate0}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='1' and fc_date=#{fcDate1}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='2' and fc_date=#{fcDate2}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='3' and fc_date=#{fcDate3}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='4' and fc_date=#{fcDate4}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            UNION ALL
            select * from t_yjy_answer
            where del_flag='0' and frequency='5' and fc_date=#{fcDate5}
            <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0'
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>


    <select id="selectTYjyFormListWithDimensionality" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown
        from (select t.* from t_yjy_form t where t.del_flag='0' order by t.dimensionality_path,t.sort_num,t.id) t
        left join
        (select i.id,i.path,i.parent_id,i.dimensionality_name,i.is_use,i.create_by,i.create_time,i.update_time,i.del_flag,i.delete_time,j.rule_type
        from t_yjy_dimensionality i left join t_yjy_dimensionality_permission j on i.id=j.dimensionality_id
        where 1=1 and i.del_flag='0' and j.work_no=#{workNo} ) p on t.dimensionality_path like concat(p.path,'%')
        <where>
             p.id is not null
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
    </select>


    <select id="selectTYjyFormListTriggerlist" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date
        from t_yjy_form t
        left join
        (select i.id,i.path,i.parent_id,i.dimensionality_name,i.is_use,i.create_by,i.create_time,i.update_time,i.del_flag,i.delete_time,j.rule_type
        from t_yjy_dimensionality i left join t_yjy_dimensionality_permission j on i.id=j.dimensionality_id
        where 1=1 and i.del_flag='0' and j.work_no=#{workNo} ) p on t.dimensionality_path like concat(p.path,'%')
        <where>
            t.del_flag='0' and p.id is not null and (t.form_type='0' or t.form_type='1')
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <select id="selectTYjyFormListTriggerlistWithNote" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date
        from t_yjy_form t
        left join
        (select i.id,i.path,i.parent_id,i.dimensionality_name,i.is_use,i.create_by,i.create_time,i.update_time,i.del_flag,i.delete_time,j.rule_type
        from t_yjy_dimensionality i left join t_yjy_dimensionality_permission j on i.id=j.dimensionality_id
        where 1=1 and i.del_flag='0' and j.work_no=#{workNo} ) p on t.dimensionality_path like concat(p.path,'%')
        <where>
            t.del_flag='0' and p.id is not null and (t.form_type='2' or t.form_type='3')
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>
            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <select id="selectForDistribute" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list,t.create_time, t.update_time, t.del_flag, t.delete_time,t.form_note,t.form_note1,t.note_dept,p.dept_code,t.maximum,t.minimum,t.unit
        from t_yjy_form t
        left join t_yjy_permission p on t.id=p.form_id
        <where>
            del_flag='0' and p.work_no = #{workNo} and p.rule_type='99'
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%') </if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%') </if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="deptCode != null  and deptCode != ''"> and t.distribute_dept like concat('%\"',#{deptCode},'%')</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>



    <select id="selectForAdministrators" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list,t.create_time, t.update_time, t.del_flag, t.delete_time,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit
        from t_yjy_form t
        left join t_yjy_dimensionality p on t.dimensionality_id=p.id
        <where>
            del_flag='0' and
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="workNo != null  and workNo != ''"> and p.create_by= #{workNo}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
<!--            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>-->
<!--            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>-->
<!--            <if test="deptCode != null  and deptCode != ''"> and p.dept_code = #{deptCode}</if>-->
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>


    <select id="selectTYjyFormListForAnswer" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list,t.create_time, t.update_time, t.del_flag, t.delete_time,t.form_note,t.form_note1,t.note_dept,p.dept_code,t.maximum,t.minimum,t.unit
        from t_yjy_form t
        left join t_yjy_permission p on t.id=p.form_id
        <where>
            t.del_flag='0' and (p.rule_type='99' or p.rule_type='0')
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
<!--            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>-->
<!--            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>-->
<!--            <if test="deptCode != null  and deptCode != ''"> and p.dept_code = #{deptCode}</if>-->
            <if test="workNo != null  and workNo != ''"> and p.work_no = #{workNo}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
--         case when form_question like '%-评价结果' then ''
--         else  form_question end desc
    </select>


    <select id="selectTYjyFormListForAnswerCount" parameterType="TYjyForm" resultMap="countResult">
        select count(1) count
        from t_yjy_form t
        left join t_yjy_permission p on t.id=p.form_id
        <where>
            t.del_flag='0' and (p.rule_type='99' or p.rule_type='0')
            <if test="id != null "> and t.id = #{id}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityId != null ">
                and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))
            </if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path = #{dimensionalityPath}</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <!--            <if test="creatorDeptName != null  and creatorDeptName != ''"> and t.creator_dept_name = #{creatorDeptName}</if>-->
            <!--            <if test="creatorDeptCode != null  and creatorDeptCode != ''"> and t.creator_dept_code = #{creatorDeptCode}</if>-->
            <!--            <if test="deptCode != null  and deptCode != ''"> and p.dept_code = #{deptCode}</if>-->
            <if test="workNo != null  and workNo != ''"> and p.work_no = #{workNo}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
        --         case when form_question like '%-评价结果' then ''
        --         else  form_question end desc
    </select>



    <select id="selectTYjyFormById" parameterType="Long" resultMap="TYjyFormResult">
        <include refid="selectTYjyFormVo"/>
        where t.id = #{id}
    </select>

    <select id="selectTYjyFormByIdWithPermission" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown,t.dept_show,t.connect_type,t.connect_id,i.rule_type
        from t_yjy_form t
        left join t_yjy_permission i on t.id=i.form_id
        where  i.work_no=#{workNo} and t.id = #{id}
    </select>

    <select id="selectWithDistribute" parameterType="TYjyForm" resultMap="TYjyFormResult">
        <include refid="selectTYjyFormVo"/>
        <where>
            t.del_flag='0' and t.distribute_dept like concat('%\"',#{distributeDept},'\"%')
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <insert id="insertTYjyForm" parameterType="TYjyForm" useGeneratedKeys="true" keyProperty="id">
        insert into t_yjy_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dimensionalityId != null">dimensionality_id,</if>
            <if test="dimensionalityPath != null">dimensionality_path,</if>
            <if test="formQuestion != null">form_question,</if>
            <if test="frequency != null">frequency,</if>
            <if test="formType != null">form_type,</if>
            <if test="creatorDeptName != null">creator_dept_name,</if>
            <if test="creatorDeptCode != null">creator_dept_code,</if>
            <if test="checkerList != null">checker_list,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="distributeDept != null">distribute_dept,</if>
            <if test="formNote != null">form_note,</if>
            <if test="formNote1 != null">form_note1,</if>
            <if test="noteDept != null">note_dept,</if>
            <if test="maximum != null">maximum,</if>
            <if test="minimum != null">minimum,</if>
            <if test="unit != null">unit,</if>
            <if test="deadlineSwitch != null">deadline_switch,</if>
            <if test="deadlineDate != null">deadline_date,</if>
            <if test="mailSwitch != null">mail_switch,</if>
            <if test="mailDate != null">mail_date,</if>
            <if test="submitDate != null">submit_date,</if>
            <if test="countdown != null">countdown,</if>
            <if test="deptShow != null">dept_show,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dimensionalityId != null">#{dimensionalityId},</if>
            <if test="dimensionalityPath != null">#{dimensionalityPath},</if>
            <if test="formQuestion != null">#{formQuestion},</if>
            <if test="frequency != null">#{frequency},</if>
            <if test="formType != null">#{formType},</if>
            <if test="creatorDeptName != null">#{creatorDeptName},</if>
            <if test="creatorDeptCode != null">#{creatorDeptCode},</if>
            <if test="checkerList != null">#{checkerList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="distributeDept != null">#{distributeDept},</if>
            <if test="formNote != null">#{formNote},</if>
            <if test="formNote1 != null">#{formNote1},</if>
            <if test="noteDept != null">#{noteDept},</if>
            <if test="maximum != null">#{maximum},</if>
            <if test="minimum != null">#{minimum},</if>
            <if test="unit != null">#{unit},</if>
            <if test="deadlineSwitch != null">#{deadlineSwitch},</if>
            <if test="deadlineDate != null">#{deadlineDate},</if>
            <if test="mailSwitch != null">#{mailSwitch},</if>
            <if test="mailDate != null">#{mailDate},</if>
            <if test="submitDate != null">#{submitDate},</if>
            <if test="countdown != null">#{countdown},</if>
            <if test="deptShow != null">#{deptShow},</if>
         </trim>
    </insert>

    <update id="updateTYjyForm" parameterType="TYjyForm">
        update t_yjy_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="dimensionalityId != null">dimensionality_id = #{dimensionalityId},</if>
            <if test="dimensionalityPath != null">dimensionality_path = #{dimensionalityPath},</if>
            <if test="formQuestion != null">form_question = #{formQuestion},</if>
            <if test="frequency != null">frequency = #{frequency},</if>
            <if test="formType != null">form_type = #{formType},</if>
            <if test="creatorDeptName != null">creator_dept_name = #{creatorDeptName},</if>
            <if test="creatorDeptCode != null">creator_dept_code = #{creatorDeptCode},</if>
            <if test="checkerList != null">checker_list = #{checkerList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="distributeDept != null">distribute_dept = #{distributeDept},</if>
            <if test="formNote != null">form_note = #{formNote},</if>
            <if test="formNote1 != null">form_note1 = #{formNote1},</if>
            <if test="noteDept != null">note_dept = #{noteDept},</if>
            <!--            <if test="maximum != null">maximum = #{maximum},</if>-->
            <!--            <if test="minimum != null">minimum = #{minimum},</if>-->
            maximum = #{maximum},
            minimum = #{minimum},
            <if test="unit != null">unit = #{unit},</if>
            <if test="deadlineSwitch != null">deadline_switch = #{deadlineSwitch},</if>
            <if test="deadlineDate != null">deadline_date = #{deadlineDate},</if>
            <if test="mailSwitch != null">mail_switch = #{mailSwitch},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="countdown != null">countdown = #{countdown},</if>
            <if test="deptShow != null">dept_show = #{deptShow},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteTYjyFormById" parameterType="Long">
        update t_yjy_form set del_flag='1' where id = #{id}
    </update>


    <update id="deleteTYjyFormBydimensionalityId" parameterType="Long">
        update t_yjy_form set del_flag='1' where dimensionality_id = #{id}
    </update>

    <update id="deleteTYjyFormByIds" parameterType="String">
        update t_yjy_form set del_flag='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <update id="deadlineUpdate" parameterType="TYjyForm">
        update t_yjy_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="deadlineSwitch != null">deadline_switch = #{deadlineSwitch},</if>
            <if test="deadlineDate != null">deadline_date = #{deadlineDate},</if>
            <if test="mailSwitch != null">mail_switch = #{mailSwitch},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="countdown != null">countdown = #{countdown},</if>
        </trim>
        where dimensionality_path like concat(#{dimensionalityPath},'%')
    </update>


    <update id="updateFormWithDeadline" parameterType="TYjyForm">
        update t_yjy_form
        <!--set deadline_switch=#{deadlineSwitch},deadline_date=#{deadlineDate},mail_date=#{mailDate},submitDate=#{deadlineDate}-->
        <trim prefix="SET" suffixOverrides=",">
            <if test="deadlineSwitch != null">deadline_switch = #{deadlineSwitch},</if>
            <if test="deadlineDate != null">deadline_date = #{deadlineDate},</if>
            <if test="mailSwitch != null">mail_switch = #{mailSwitch},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="countdown != null">countdown = #{countdown},</if>
        </trim>
        where id =#{id}
    </update>


    <update id="updateFormWithMail" parameterType="TYjyForm">
        update t_yjy_form
        <trim prefix="SET" suffixOverrides=",">
            <if test="mailSwitch != null">mail_switch = #{mailSwitch},</if>
            <if test="mailDate != null">mail_date = #{mailDate},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="countdown != null">countdown = #{countdown},</if>
        </trim>
        where id =#{id}
    </update>


    <update id="mailUpdate" parameterType="TYjyForm">
        update t_yjy_form set mail_switch=#{mailSwitch}
        where dimensionality_path like concat(#{dimensionalityPath},'%')
    </update>





    <!--<select id="selectFormListForMail" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,IFNULL(j.status,'4') status,j.form_value,j.check_work_no,j.check_user_name
        from
        (
        select distinct t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.sort_num,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date
        from t_yjy_form t left join t_yjy_permission i on t.id=i.form_id
        where t.deadline_switch='1' and t.mail_switch='1' and #{fcDate}>=t.mail_date and t.del_flag='0'
        <if test="id != null "> and t.id = #{id}</if>
        <if test="dimensionalityId != null ">
            and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))
        </if>
        )t left join t_yjy_answer j on t.id=j.form_id
        <where>
            t.del_flag='0' and ( j.id is null or j.del_flag='0') and (t.submit_date=j.fc_date or j.fc_date is null)
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select> -->


    <select id="selectFormListForMail" parameterType="TYjyForm" resultMap="TYjyFormResult">
        select distinct t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,
        IFNULL((case when t.submit_date!=j.fc_date then null else j.status end),'4') as status,
        (case when t.submit_date!=j.fc_date then null else j.form_value end) as form_value,
        (case when t.submit_date!=j.fc_date then null else j.check_work_no end) as check_work_no,
        (case when t.submit_date!=j.fc_date then null else j.check_user_name end) as check_user_name
        from
        (select distinct t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.sort_num,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date
        from t_yjy_form t left join t_yjy_permission i on t.id=i.form_id
        where t.deadline_switch='1' and t.mail_switch='1' and #{fcDate}>=t.mail_date and t.del_flag='0'
        <if test="id != null "> and t.id = #{id}</if>
        <if test="dimensionalityId != null ">
            and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))
        </if>
        )t
        left join t_yjy_answer j on concat(CONVERT(t.id, CHAR),' ',t.submit_date) = concat(CONVERT(j.form_id, CHAR),' ',j.fc_date)
        <where>
            t.del_flag='0' and ( j.id is null or j.del_flag='0')
        </where>
        order by t.dimensionality_path,t.id
    </select>



    <select id="selectFrequencyCount" parameterType="TYjyForm" resultMap="countResult">
        select count(t.id) count
        from t_yjy_form t
        <where>
            del_flag='0' and (t.frequency='0' or  t.frequency='5')
            <if test="dimensionalityId != null "> and (t.dimensionality_path like  concat(CONVERT(#{dimensionalityId}, CHAR),',%') or t.dimensionality_path like concat('%,',CONVERT(#{dimensionalityId}, CHAR),',%'))</if>
        </where>
    </select>

    <select id="selectExportListForAdmin" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id, t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.connect_type,t.connect_id,j.fc_date,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.creator_no,j.creator_name,j.reason,j.measure,j.assessment
        from t_yjy_form t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date>=#{fcDate0} and #{fcDate00}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date>=#{fcDate1} and #{fcDate11}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date>=#{fcDate2} and #{fcDate22}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date>=#{fcDate3} and #{fcDate33}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date>=#{fcDate4} and #{fcDate44}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date>=#{fcDate5} and #{fcDate55}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0' and ( j.id is null or j.del_flag='0')
            <if test="id != null "> and t.id = #{id} </if>
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>

    <select id="selectExportListForSubmit" parameterType="TYjyFormAnswer" resultMap="TYjyFormResult">
        select t.id,t.dimensionality_id,t.dimensionality_path, t.form_question, t.frequency,t.form_type,t.creator_dept_name,t.creator_dept_code,t.checker_list, t.create_time, t.update_time, t.del_flag, t.delete_time,t.distribute_dept,t.form_note,t.form_note1,t.note_dept,t.maximum,t.minimum,t.unit,t.dimensionality_name_export,t.form_question_export,t.deadline_switch,t.deadline_date,t.mail_switch,t.mail_date,t.submit_date,t.countdown,t.dept_show,t.connect_type,t.connect_id,j.fc_date,IFNULL(j.status,'4') status,j.form_value,j.form_file,j.creator_no,j.creator_name,j.assessment,j.check_work_no,j.check_user_name,j.id as answer_id,j.version,j.check_history,j.reason,j.measure
        from
        (
        select t.*
        from t_yjy_form t left join t_yjy_permission i on t.id=i.form_id
        where  i.work_no=#{workNo}
        <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        <!--where  i.work_no=#{workNo} and i.rule_type='99'-->
        )t left join
        (
        select * from t_yjy_answer
        where del_flag='0' and frequency='0' and fc_date>=#{fcDate0} and #{fcDate00}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='1' and fc_date>=#{fcDate1} and #{fcDate11}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='2' and fc_date>=#{fcDate2} and #{fcDate22}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='3' and fc_date>=#{fcDate3} and #{fcDate33}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='4' and fc_date>=#{fcDate4} and #{fcDate44}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        UNION ALL
        select * from t_yjy_answer
        where del_flag='0' and frequency='5' and fc_date>=#{fcDate5} and #{fcDate55}>=fc_date
        <if test="dimensionalityPath != null "> and dimensionality_path like concat(#{dimensionalityPath},'%')</if>
        ) j on t.id=j.form_id
        <where>
            t.del_flag='0'
            <if test="formQuestion != null  and formQuestion != ''"> and t.form_question like concat('%',#{formQuestion},'%')</if>
            <if test="dimensionalityPath != null "> and t.dimensionality_path like concat(#{dimensionalityPath},'%')</if>
            <if test="frequency != null  and frequency != ''"> and t.frequency = #{frequency}</if>
            <if test="formType != null  and formType != ''"> and t.form_type = #{formType}</if>
            <if test="status != null and status != '4' "> and IFNULL(j.status,'4') = #{status}</if>
            <if test="deadlineSwitch != null  and deadlineSwitch != ''"> and t.deadline_switch = #{deadlineSwitch}</if>
            <if test="distributeDept != null  and distributeDept != ''"> and t.distribute_dept like concat('%\"',#{distributeDept},'%')</if>
            <if test="mailSwitch != null  and mailSwitch != ''"> and t.mail_switch = #{mailSwitch}</if>
        </where>
        order by t.dimensionality_path,t.sort_num,t.id
    </select>


</mapper>