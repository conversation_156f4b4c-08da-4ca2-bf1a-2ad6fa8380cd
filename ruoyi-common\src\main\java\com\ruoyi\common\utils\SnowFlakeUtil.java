package com.ruoyi.common.utils;

import cn.hutool.core.lang.Singleton;

/**
 * 雪花算法工具类
 */
public class SnowFlakeUtil {
    private static final long START_STMP = 1420041600000L;
    private static final long SEQUENCE_BIT = 9L;
    private static final long MACHINE_BIT = 2L;
    private static final long DATACENTER_BIT = 2L;
    private static final long MAX_SEQUENCE = 511L;
    private static final long MAX_MACHINE_NUM = 3L;
    private static final long MAX_DATACENTER_NUM = 3L;
    private static final long MACHINE_LEFT = 9L;
    private static final long DATACENTER_LEFT = 11L;
    private static final long TIMESTMP_LEFT = 13L;
    private long datacenterId;
    private long machineId;
    private long sequence = 0L;
    private long lastStmp = -1L;

    public SnowFlakeUtil(long datacenterId, long machineId) {
        if (datacenterId <= 3L && datacenterId >= 0L) {
            if (machineId <= 3L && machineId >= 0L) {
                this.datacenterId = datacenterId;
                this.machineId = machineId;
            } else {
                throw new IllegalArgumentException("machineId can't be greater than MAX_MACHINE_NUM or less than 0");
            }
        } else {
            throw new IllegalArgumentException("datacenterId can't be greater than MAX_DATACENTER_NUM or less than 0");
        }
    }

    public synchronized long nextId() {
        long currStmp = this.getNewstmp();
        if (currStmp < this.lastStmp) {
            throw new RuntimeException("Clock moved backwards.  Refusing to generate id");
        } else {
            if (currStmp == this.lastStmp) {
                this.sequence = this.sequence + 1L & 511L;
                if (this.sequence == 0L) {
                    currStmp = this.getNextMill();
                }
            } else {
                this.sequence = 0L;
            }

            this.lastStmp = currStmp;
            return currStmp - 1420041600000L << 13 | this.datacenterId << 11 | this.machineId << 9 | this.sequence;
        }
    }

    private long getNextMill() {
        long mill;
        for(mill = this.getNewstmp(); mill <= this.lastStmp; mill = this.getNewstmp()) {
        }

        return mill;
    }

    private long getNewstmp() {
        return System.currentTimeMillis();
    }


    //吨钢承包批次编号生成
    public static String getDGCBSupplyNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //吨钢承包流水号生成
    public static String getDGCBFlowNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //吨钢承包返修编号生成
    public static String getDGCBRepairNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //吨钢承包返厂编号生成
    public static String getDGCBReturnNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //部门通行证审批流编号生成
    public static String getDeptPassportReturnNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //跟踪督办事件编号生成
    public static String getEventTrackReturnNoSnowFlakeId(String roleKey) {
        String tag = roleKey.replace("et-","").toUpperCase();

        return tag + String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //违章单编号生成
    public static String getViolateTicketNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //支付单编号生成
    public static String getPaymentNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //货车预约单编号生成
    public static String getTruckTicketNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //废钢预约单编号生成
    public static String getScrapSteelTicketNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }
    //废钢预约单排队号生成
    public static String getScrapSteelTicketQueueNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //货车预约模板编号生成
    public static String getTruckUnifyTemplateNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //访客预约编号生成
    public static String getVisitorTemplateNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //访客流水号生成
    public static String getVisitorNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //汽吊流水生成
    public static String getCraneNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //合金预约单编号生成
    public static String getAlloyReservationNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    //合金预约单推送编号生成
    public static String getAlloyPushNoSnowFlakeId() {
        return String.valueOf(((SnowFlakeUtil) Singleton.get(SnowFlakeUtil.class, new Object[]{1L, 1L})).nextId());
    }

    public static void main(String[] args) {
        for(int i = 0; i < 10; ++i) {
            System.out.println(getDGCBSupplyNoSnowFlakeId());
//            System.out.println(getDGCBSupplyNoSnowFlakeId().toString().length());
        }

    }
}
