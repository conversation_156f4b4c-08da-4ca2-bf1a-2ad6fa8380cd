<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.TYjyFormHistoryMapper">
    
    <resultMap type="TYjyFormHistory" id="TYjyFormHistoryResult">
        <result property="id"    column="id"    />
        <result property="formId"    column="form_id"    />
        <result property="dimensionalityPath"    column="dimensionality_path"    />
        <result property="formQuestion"    column="form_question"    />
        <result property="noteDept"    column="note_dept"    />
        <result property="formNote"    column="form_note"    />
        <result property="formNote1"    column="form_note1"    />
        <result property="maximum"    column="maximum"    />
        <result property="minimum"    column="minimum"    />
        <result property="unit"    column="unit"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTYjyFormHistoryVo">
        select id, form_id,dimensionality_path, form_question, note_dept, form_note, form_note1, maximum, minimum, unit ,update_time,update_by
        from t_yjy_form_history
    </sql>

    <select id="selectTYjyFormHistoryList" parameterType="TYjyFormHistory" resultMap="TYjyFormHistoryResult">
        <include refid="selectTYjyFormHistoryVo"/>
        <where>  
            <if test="formId != null "> and form_id = #{formId}</if>
            <if test="formQuestion != null  and formQuestion != ''"> and form_question = #{formQuestion}</if>
            <if test="dimensionalityPath != null  and dimensionalityPath != ''"> and dimensionality_path = #{dimensionalityPath}</if>
            <if test="noteDept != null  and noteDept != ''"> and note_dept = #{noteDept}</if>
            <if test="formNote != null  and formNote != ''"> and form_note = #{formNote}</if>
            <if test="formNote1 != null  and formNote1 != ''"> and form_note1 = #{formNote1}</if>
            <if test="maximum != null "> and maximum = #{maximum}</if>
            <if test="minimum != null "> and minimum = #{minimum}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
        </where>
    </select>
    
    <select id="selectTYjyFormHistoryById" parameterType="Long" resultMap="TYjyFormHistoryResult">
        <include refid="selectTYjyFormHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTYjyFormHistory" parameterType="TYjyFormHistory" useGeneratedKeys="true" keyProperty="id">
        insert into t_yjy_form_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null">form_id,</if>
            <if test="formQuestion != null">form_question,</if>
            <if test="dimensionalityPath != null">dimensionality_path,</if>
            <if test="noteDept != null">note_dept,</if>
            <if test="formNote != null">form_note,</if>
            <if test="formNote1 != null">form_note1,</if>
            <if test="maximum != null">maximum,</if>
            <if test="minimum != null">minimum,</if>
            <if test="unit != null">unit,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null">#{formId},</if>
            <if test="formQuestion != null">#{formQuestion},</if>
            <if test="dimensionalityPath != null">#{dimensionalityPath},</if>
            <if test="noteDept != null">#{noteDept},</if>
            <if test="formNote != null">#{formNote},</if>
            <if test="formNote1 != null">#{formNote1},</if>
            <if test="maximum != null">#{maximum},</if>
            <if test="minimum != null">#{minimum},</if>
            <if test="unit != null">#{unit},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateTYjyFormHistory" parameterType="TYjyFormHistory">
        update t_yjy_form_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="formId != null">form_id = #{formId},</if>
            <if test="formQuestion != null">form_question = #{formQuestion},</if>
            <if test="dimensionalityPath != null">dimensionality_path = #{dimensionalityPath},</if>
            <if test="noteDept != null">note_dept = #{noteDept},</if>
            <if test="formNote != null">form_note = #{formNote},</if>
            <if test="formNote1 != null">form_note1 = #{formNote1},</if>
            <if test="maximum != null">maximum = #{maximum},</if>
            <if test="minimum != null">minimum = #{minimum},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTYjyFormHistoryById" parameterType="Long">
        delete from t_yjy_form_history where id = #{id}
    </delete>

    <delete id="deleteTYjyFormHistoryByIds" parameterType="String">
        delete from t_yjy_form_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper>