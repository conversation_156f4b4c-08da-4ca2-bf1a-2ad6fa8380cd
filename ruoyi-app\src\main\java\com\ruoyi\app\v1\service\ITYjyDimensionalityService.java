package com.ruoyi.app.v1.service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.eventTrack.domain.TEtEvent;
import com.ruoyi.app.jw.domain.LeaderInfo;
import com.ruoyi.app.templateFile.domain.TemplateFile;
import com.ruoyi.app.v1.domain.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * TYjyDimensionalityService接口
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface ITYjyDimensionalityService 
{
    /**
     * 查询TYjyDimensionality
     * 
     * @param id TYjyDimensionalityID
     * @return TYjyDimensionality
     */
    public TYjyDimensionality selectTYjyDimensionalityById(Long id);

    /**
     * 查询TYjyDimensionality列表
     * 
     * @param tYjyDimensionality TYjyDimensionality
     * @return TYjyDimensionality集合
     */
    public List<TYjyDimensionality> selectTYjyDimensionalityList(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality>  selectTYjyDimensionalityListIsExit(TYjyDimensionality tYjyDimensionality);
    /**
     * 查询根节点
     *
     * @param tYjyDimensionality TYjyDimensionality
     * @return TYjyDimensionality集合
     */
    public List<TYjyDimensionality> selectRootList(TYjyDimensionality tYjyDimensionality);

    /**
     * 查询根节点的子节点
     *
     * @param tYjyDimensionality TYjyDimensionality
     * @return TYjyDimensionality集合
     */
    public List<TYjyDimensionality> getByRootId(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getByRootIdForUser(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getALLRootForAnswer(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getALLRootFordistribute(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getALLparentRootFordistribute(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getALLparentRootFordistributePlus(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getByRootIdwithParent(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> getChildremById(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> selectRootListWithDept(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> selectRootListWithDeptPlus(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> selectRootListWithDeptSuper(TYjyDimensionality tYjyDimensionality);

    public List<JSONObject> dealLsitWithStatus(List<TYjyDimensionality> list,String fcDate);
    public List<JSONObject> dealLsitWithStatusPlus(List<TYjyDimensionality> list,String fcDate);

    public List<JSONObject> dealLsitWithsubmit(List<TYjyDimensionality> list,String fcDate,String WorkNo);
    public List<JSONObject> dealLsitWithsubmitPlus(List<TYjyDimensionality> list,String fcDate,String WorkNo);

    public List<TYjyForm> getFormStatusListWithsumbit(String path,String fcDate,String frequency,String status,String workNo);

    public List<TYjyForm> getFormStatusListWithadmin(String path, String fcDate, String frequency,String status);

    public List<TYjyForm> getformstatus(String path,String fcDate,String formQuestion);

    public List<TYjyForm> getformstatusSubmit(String path,String fcDate,String formQuestion,String WorkNo);
    //计算日期的函数
    public String dealfcdate(String fcDate,String frequency);

    public String dealfcdatebefore(String fcDate,String frequency,int num);

    /**
     * 新增TYjyDimensionality
     * 
     * @param tYjyDimensionality TYjyDimensionality
     * @return 结果
     */
    public int insertTYjyDimensionality(TYjyDimensionality tYjyDimensionality);

    /**
     * 修改TYjyDimensionality
     * 
     * @param tYjyDimensionality TYjyDimensionality
     * @return 结果
     */
    public int updateTYjyDimensionality(TYjyDimensionality tYjyDimensionality);

    /**
     * 批量删除TYjyDimensionality
     * 
     * @param ids 需要删除的TYjyDimensionalityID
     * @return 结果
     */
    public int deleteTYjyDimensionalityByIds(Long[] ids);

    /**
     * 删除TYjyDimensionality信息
     * 
     * @param id TYjyDimensionalityID
     * @return 结果
     */
    public int deleteTYjyDimensionalityById(Long id);

    public TYjyDimensionalityRoot getTree(TYjyDimensionalityRoot tYjyDimensionalityRoot, Map<Long,List<TYjyDimensionalityRoot>> map);


    public TYjyDimensionality getALlTree(TYjyDimensionality tYjyDimensionality, Map<Long,List<TYjyDimensionality>> map);


    public List<DeptInfoList> getDeptInfoList();

    public void export(HttpServletResponse response, String startDate,String endDate,Long rootId) throws IOException;
    public void exportTemplate(HttpServletResponse response, String startDate,String endDate,Long rootId) throws IOException;

    public Map<String, List<JSONObject>> readExcelBySheetNames(MultipartFile file, Set<String> targetSheets) throws IOException;


    public void readExcelBySheetSpecial(MultipartFile file) throws IOException;



    public void exportOnce(HttpServletResponse response, String fcDate, Long rootId,String type)  throws IOException;



    public void exportTwice(HttpServletResponse response, String startDate, String endDate, Long rootId,String type)  throws IOException;

    public void exportThird(HttpServletResponse response, String startDate, String endDate, Long rootId,String type)  throws IOException;

    public void exportTemplate(HttpServletResponse response, String fcDate, Long rootId,String type)  throws IOException;

    public List<TYjyDimensionality> selectDeadlineForDimensionality(TYjyDimensionality tYjyDimensionality);


    public List<TYjyDimensionality> selectDeadlineForDimensionalityForUser(TYjyDimensionality tYjyDimensionality);

    public List<TYjyDimensionality> selectDeadlineForDimensionalityForAdmin(TYjyDimensionality tYjyDimensionality);

//    public Workbook ExportFileTemplate(Map<String,TYjyForm> formMap, TemplateFile template, String fcDate) throws Exception;
}
