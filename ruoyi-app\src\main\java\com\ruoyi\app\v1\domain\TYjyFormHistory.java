package com.ruoyi.app.v1.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * TYjyFormHistory对象 t_yjy_form_history
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
public class TYjyFormHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long formId;

    /** 填报问题 */
    @Excel(name = "填报问题")
    private String formQuestion;

    private String dimensionalityPath;
    /** 指标单位名称 */
    @Excel(name = "指标单位名称")
    private String noteDept;

    /** 问题指标 */
    @Excel(name = "问题指标")
    private String formNote;

    /** 问题备注1 */
    @Excel(name = "问题备注1")
    private String formNote1;

    /** 最大值 */
    @Excel(name = "最大值")
    private Double maximum;

    /** 最小值 */
    @Excel(name = "最小值")
    private Double minimum;

    /** 问题单位 */
    @Excel(name = "问题单位")
    private String unit;

    private String updateBy;
    private Date updateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFormId(Long formId) 
    {
        this.formId = formId;
    }

    public Long getFormId() 
    {
        return formId;
    }
    public void setFormQuestion(String formQuestion) 
    {
        this.formQuestion = formQuestion;
    }

    public String getFormQuestion() 
    {
        return formQuestion;
    }
    public void setNoteDept(String noteDept) 
    {
        this.noteDept = noteDept;
    }

    public String getNoteDept() 
    {
        return noteDept;
    }
    public void setFormNote(String formNote) 
    {
        this.formNote = formNote;
    }

    public String getFormNote() 
    {
        return formNote;
    }
    public void setFormNote1(String formNote1) 
    {
        this.formNote1 = formNote1;
    }

    public String getFormNote1() 
    {
        return formNote1;
    }
    public void setMaximum(Double maximum) 
    {
        this.maximum = maximum;
    }

    public Double getMaximum() 
    {
        return maximum;
    }
    public void setMinimum(Double minimum) 
    {
        this.minimum = minimum;
    }

    public Double getMinimum() 
    {
        return minimum;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }

    public String getDimensionalityPath() {
        return dimensionalityPath;
    }

    public void setDimensionalityPath(String dimensionalityPath) {
        this.dimensionalityPath = dimensionalityPath;
    }

    @Override
    public String getUpdateBy() {
        return updateBy;
    }

    @Override
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public Date getUpdateTime() {
        return updateTime;
    }

    @Override
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("formId", getFormId())
            .append("formQuestion", getFormQuestion())
            .append("noteDept", getNoteDept())
            .append("formNote", getFormNote())
            .append("formNote1", getFormNote1())
            .append("maximum", getMaximum())
            .append("minimum", getMinimum())
            .append("unit", getUnit())
            .toString();
    }
}
