package com.ruoyi.common.utils.EasyExcel;

import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 根据模版导出Excel工具类
 * <AUTHOR>
 * @Date 2024/2/19
 */
public class ExportExcelByTemplateUtils {

    /**
     * @description: 根据模版导出Excel入口（单个list数据）
     * @param templateFileName
     * @param exportFilePathAndName
     * @param staticDataMap
     * @param dataId
     * @param originDataList
     * @return void
     * <AUTHOR>
     * @date 2024/2/21
     */
    public static void doExportExcelOneListByTemplate(String templateFileName, String exportFilePathAndName,
                                                      Map<String, Object> staticDataMap,
                                                      String dataId,
                                                      List<?> originDataList) throws Exception{

        List<Map<String, Object>> exportDataList = MapObjectUtil.objListToMapList(originDataList);
        // 只有一个list数据
        List<DynamicDataMapping> dynamicDataMappingList = DynamicDataMapping.createOneDataList(dataId, exportDataList);
        // 导出
        ExcelTemplateProc.doExportExcelByTemplateProc(templateFileName,exportFilePathAndName,staticDataMap,dynamicDataMappingList);

    }

    public static void doExportExcelOneListByTemplateWithSign(String templateFileName, String exportFilePathAndName,
                                                      Map<String, Object> staticDataMap,
                                                      String dataId,
                                                      List<?> originDataList,boolean isSign, String imageUrl, int rowNum, int colNum) throws Exception{

        List<Map<String, Object>> exportDataList = MapObjectUtil.objListToMapList(originDataList);
        // 只有一个list数据
        List<DynamicDataMapping> dynamicDataMappingList = DynamicDataMapping.createOneDataList(dataId, exportDataList);
        // 导出
        ExcelTemplateProc.doExportExcelByTemplateProcWithSign(templateFileName,exportFilePathAndName,staticDataMap,dynamicDataMappingList,isSign,imageUrl,rowNum,colNum);
    }

    /**
     * @description: 根据模版导出Excel入口（多个list数据）
     * @param templateFileName
     * @param exportFilePathAndName
     * @param staticSource
     * @param originDataMapList
     * @return void
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static void doExportExcelMoreListByTemplateWithSign(String templateFileName,
                                                       String exportFilePathAndName,
                                                       Map<String, Object> staticSource,
                                                       Map<String, List<?>> originDataMapList,boolean isSign, String imageUrl, int rowNum, int colNum) throws Exception{

        Map<String,List<Map<String, Object>>> transMap = new HashMap<>();
        originDataMapList.forEach((dataId,originDataList)->{
            List<Map<String, Object>> transDataList = MapObjectUtil.objListToMapList(originDataList);
            transMap.put(dataId,transDataList);
        });
        // 多个list类型数据
        List<DynamicDataMapping> dynamicDataMappingList = DynamicDataMapping.createMorDataList(transMap);
        // 导出
        ExcelTemplateProc.doExportExcelByTemplateProcWithSign(templateFileName,exportFilePathAndName,staticSource,dynamicDataMappingList,isSign,imageUrl,rowNum,colNum);
    }

    public static void doExportExcelMoreListMoreSheetByTemplateWithSign(String templateFileName,
                                                               String exportFilePathAndName,
                                                               List<Map<String, Object>> staticSource,
                                                               List<Map<String, List<?>>> originDataMapList,List<Boolean> isSigns, List<String> imageUrls, List<Integer> rowNums, List<Integer> colNums) throws Exception{

        List<List<DynamicDataMapping>> dynamicDataMappingList = new ArrayList();
        originDataMapList.forEach(list->{
            Map<String,List<Map<String, Object>>> transMap = new HashMap<>();
            list.forEach((dataId,originDataList)->{
                List<Map<String, Object>> transDataList = MapObjectUtil.objListToMapList(originDataList);
                transMap.put(dataId,transDataList);
            });
            // 多个list类型数据
            List<DynamicDataMapping> param = DynamicDataMapping.createMorDataList(transMap);
            dynamicDataMappingList.add(param);
        });
        // 导出
        ExcelTemplateProc.doExportExcelByTemplateProcWithSign(templateFileName,exportFilePathAndName,staticSource,dynamicDataMappingList,isSigns,imageUrls,rowNums,colNums);
    }

    public static void doExportExcelMoreListByTemplate(String templateFileName,
                                                       String exportFilePathAndName,
                                                       Map<String, Object> staticSource,
                                                       Map<String, List<?>> originDataMapList) throws Exception{

        Map<String,List<Map<String, Object>>> transMap = new HashMap<>();
        originDataMapList.forEach((dataId,originDataList)->{
            List<Map<String, Object>> transDataList = MapObjectUtil.objListToMapList(originDataList);
            transMap.put(dataId,transDataList);
        });
        // 多个list类型数据
        List<DynamicDataMapping> dynamicDataMappingList = DynamicDataMapping.createMorDataList(transMap);
        // 导出
        ExcelTemplateProc.doExportExcelByTemplateProc(templateFileName,exportFilePathAndName,staticSource,dynamicDataMappingList);

    }


    //不将文件保存在本地，直接返回Workbook文件
    public static XSSFWorkbook doExportExcelOneListByTemplateWithSignByQcy(String templateFileName,
                                                                           Map<String, Object> staticDataMap,
                                                                           Map<String, JSONObject> jsonMap,
                                                                           String dataId,
                                                                           List<?> originDataList) throws Exception{

        List<Map<String, Object>> exportDataList = MapObjectUtil.objListToMapList(originDataList);
        // 只有一个list数据
        List<DynamicDataMapping> dynamicDataMappingList = DynamicDataMapping.createOneDataList(dataId, exportDataList);
        // 导出
        return ExcelTemplateProc.doExportExcelByTemplateProcByQcy(templateFileName,staticDataMap,jsonMap,dynamicDataMappingList);
    }

    public static File doenloadFileByQcy(String templateFileName) throws Exception
    {
        return ExcelTemplateProc.doenloadFileByQcy(templateFileName);
    }
}
