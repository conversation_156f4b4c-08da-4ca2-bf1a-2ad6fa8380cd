package com.ruoyi.app.truck.alloy.enums;

import com.ruoyi.app.truck.alloy.domain.MatAlloyInfo;
import com.ruoyi.app.visualizingAlloy.domain.AlloyInfo;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public enum MatAlloyType {
    // A类合金 (编码1-18)
    ORDINARY_FERROSILICON("普通硅铁", "A", 1),
    FERROSILICON_1("硅铁-1", "A", 2),
    FERROSILICON_2("硅铁-2", "A", 3),
    FERROSILICON_3("硅铁-3", "A", 4),
    HIGH_CARBON_FERROMANGANESE("高碳锰铁", "A", 5),
    MEDIUM_CARBON_FERROMANGANESE("中碳锰铁", "A", 6),
    LOW_CARBON_FERROMANGANESE("低碳锰铁", "A", 7),
    SILICOMANGANESE_ALLOY("硅锰合金", "A", 8),
    SILICOMANGANESE_1_2("硅锰-1.-2", "A", 9),
    XC_MANGANESE("XC锰", "A", 10),
    ELECTROLYTIC_MANGANESE("电解锰", "A", 11),
    XC_HIGH_CHROMIUM("XC高铬", "A", 12),
    HIGH_CHROMIUM_1("高铬—1", "A", 13),
    CHARGE_GRADE_HC_FERROCHROME("炉料级高碳铬铁", "A", 14),
    MEDIUM_CARBON_FERROCHROME("中碳铬铁", "A", 15),
    LOW_CARBON_FERROCHROME("低碳铬铁", "A", 16),
    MICRO_CARBON_FERROCHROME("微碳铬铁", "A", 17),
    NITRIDED_CHROMIUM("氮化铬铁", "A", 18),

    // B类合金 (编码51-68)
    METALLIC_CHROMIUM("金属铬", "B", 51),
    TITANIUM_ORE("钛矿", "B", 52),
    FERRO_PHOSPHORUS("磷铁", "B", 53),
    FERRO_BORON("硼铁", "B", 54),
    FERRO_SULFUR("硫铁", "B", 55),
    FERRO_MOLYBDENUM("钼铁", "B", 56),
    FERRO_VANADIUM("钒铁", "B", 57),
//    ELECTROLYTIC_NICKEL("电解镍", "B", 58),
    FERRONICKEL("镍铁", "B", 58),
    WATER_QUENCHED_NICKEL("水淬镍", "B", 59),
//    NIOBIUM_SAND("铌砂", "B", 61),
    HIGH_PURITY_LANTHANUM("高纯稀土镧", "B", 60),
    FERRO_TUNGSTEN("钨铁", "B", 61),
//    ELECTROLYTIC_COPPER("电解铜", "B", 64),
    METALLIC_COBALT("金属钴", "B", 62),
    ANTIMONY("锑", "B", 63),
    CORE_WIRE_TYPE("芯线类", "B", 64),
    ALUMINUM_PRODUCTS("铝制品", "B", 65),
    FERROSILICON_POWDER("硅铁粉", "B", 66),
    SILICON_CALCIUM_POWDER("硅钙粉", "B", 67),
    ALUMINUM_CALCIUM_DEOXIDIZER("铝钙复合脱氧剂", "B", 68),

    // C类合金 (编码101-103)
    ELECTROLYTIC_NICKEL("电解镍", "C", 101),
    NIOBIUM_SAND("铌砂", "C", 102),
    ELECTROLYTIC_COPPER("电解铜", "C", 103),

    //电极
    ELECTRODE("电极", " ", 200);


    private final String displayName;
    private final String category;
    private final int code;         // 固定编码

    // 每个合金的编码是固定的
    MatAlloyType(String displayName, String category, int code) {
        this.displayName = displayName;
        this.category = category;
        this.code = code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getCategory() {
        return category;
    }

    public int getCode() {
        return code;
    }

    // 根据类别获取合金列表
    public static List<MatAlloyType> getByCategory(String category) {
        return Collections.unmodifiableList(
                Arrays.stream(values())
                        .filter(a -> a.category.equals(category))
                        .collect(Collectors.toList())
        );
    }

    // 获取全部合金列表（按编码排序）
    public static List<MatAlloyInfo> getAllAlloyInfo() {
        return Arrays.stream(values())
                .map(alloy -> new MatAlloyInfo(
                        alloy.getCode(),
                        alloy.getCategory(),
                        alloy.getDisplayName()))
                .collect(Collectors.toList());
    }

    // 根据编码获取合金类型
    public static MatAlloyType fromCode(int code) {
        return Arrays.stream(values())
                .filter(a -> a.code == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Invalid alloy code: " + code));
    }

    // 根据编码获取中文名称
    public static String getChineseNameByCode(int code) {
        return Arrays.stream(values())
                .filter(a -> a.code == code)
                .findFirst()
                .map(MatAlloyType::getDisplayName)  // 获取中文名称
                .orElse("");
    }

}
