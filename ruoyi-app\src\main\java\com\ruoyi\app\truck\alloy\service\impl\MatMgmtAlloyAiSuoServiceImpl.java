package com.ruoyi.app.truck.alloy.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.truck.alloy.domain.MatMgmtAlloyReservation;
import com.ruoyi.app.truck.alloy.service.IMatMgmtAlloyAiSuoService;
import com.ruoyi.app.truck.common.enums.DoorEnum;
import com.ruoyi.app.vehicleAccess.common.utils.RequestUtils;
import com.ruoyi.app.vehicleAccess.domain.AccessInfo;
import com.ruoyi.app.vehicleAccess.enums.CarLicensePlateColor;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SnowFlakeUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 合金车辆预约推送爱索接口Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class MatMgmtAlloyAiSuoServiceImpl implements IMatMgmtAlloyAiSuoService {

    private static final Logger log = LoggerFactory.getLogger(MatMgmtAlloyAiSuoServiceImpl.class);

    /**
     * 推送合金车辆信息
     *
     * @param reservation 合金车辆预约信息
     */
    @Override
    public void pushTruckInfo(MatMgmtAlloyReservation reservation) {
        try {
            AccessInfo accessInfo = this.convertToAccessInfo(reservation);
            String param = RequestUtils.parseUrlParams(accessInfo);
            log.info("合金车辆推送参数：{}", param);

            String url = "http://172.16.13.222:8080/asseapi/api/v1/exts/xccarsinfo";
            log.info("MatMgmtAlloyAiSuoServiceImpl.pushTruckInfo=>request: {}", accessInfo);

            String result = HttpUtils.sendPostToVehicle(url, param);
            log.info("MatMgmtAlloyAiSuoServiceImpl.pushTruckInfo=>response: {}", result);

        } catch (Exception e) {
            log.error("推送合金车辆信息失败 - 预约编号：{}", reservation.getReservationNo(), e);
            throw new RuntimeException("推送合金车辆信息失败：" + e.getMessage());
        }
    }

    /**
     * 删除推送合金车辆信息
     *
     * @param reservation 合金车辆预约信息
     */
    @Override
    public void pushDeleteTruckInfo(MatMgmtAlloyReservation reservation) throws UnsupportedEncodingException {
        try {
            AccessInfo accessInfo = this.convertToDeleteInfo(reservation);
            String param = RequestUtils.parseUrlParams(accessInfo);
            log.info("合金车辆删除推送参数：{}", param);
            log.info("MatMgmtAlloyAiSuoServiceImpl.pushDeleteTruckInfo=>request: {}", accessInfo);

            String url = "http://172.16.13.222:8080/asseapi/api/v1/exts/delonesyndata?carNo="
                    + URLEncoder.encode(reservation.getCarNo(), "UTF-8")
                    + "&carSerialNo=" + URLEncoder.encode(reservation.getReservationNo(), "UTF-8")
                    + "&applyDate=" + URLEncoder.encode(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()), "UTF-8");

            int result = HttpUtils.doDeleteVehicle(url);
            log.info("MatMgmtAlloyAiSuoServiceImpl.pushDeleteTruckInfo=>response: {}", result);

        } catch (Exception e) {
            log.error("删除推送合金车辆信息失败 - 预约编号：{}", reservation.getReservationNo(), e);
            throw new RuntimeException("删除推送合金车辆信息失败：" + e.getMessage());
        }
    }

    /**
     * 转换为AccessInfo对象（推送用）
     */
    private AccessInfo convertToAccessInfo(MatMgmtAlloyReservation reservation) {
        AccessInfo accessInfo = new AccessInfo();

        // 基本信息
        accessInfo.setCarSerialNo(reservation.getPushNo());
        accessInfo.setIsCheck(1);
        accessInfo.setTimes(0);
        accessInfo.setCheckManager("物管部");
        accessInfo.setCarNo(reservation.getCarNo());
        accessInfo.setCarNoColor(CarLicensePlateColor.getDescriptionByCode(reservation.getLicensePlateColor()));
        accessInfo.setCarType("货车");
        accessInfo.setIsPushOrder(1);
        accessInfo.setApplyDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getNowDate()));

        // 有效时间段
        if (reservation.getEffectiveStartTime() != null && reservation.getEffectiveEndTime() != null) {
            accessInfo.setPredictEntryDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, reservation.getEffectiveStartTime()));
            accessInfo.setPredictEntryBeginDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, reservation.getEffectiveStartTime()));
            accessInfo.setPredictEntryEndDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, reservation.getEffectiveEndTime()));
        }

        // 申请人信息
        accessInfo.setApplyAccountName(StringUtils.isNotBlank(reservation.getBusinessApprover()) ?
                reservation.getBusinessApprover() : "未知申请人");
        accessInfo.setApplyAccountDName("物管部");

        // 司机信息
        accessInfo.setDriver1(StringUtils.isNotBlank(reservation.getDriverName()) ?
                reservation.getDriverName() : "未知司机");
        accessInfo.setCarPin(StringUtils.isNotBlank(reservation.getDriverCardNo()) ?
                reservation.getDriverCardNo() : "");

        // 公司信息
        accessInfo.setCompany(reservation.getApplyCompanyName());
        accessInfo.setEntryReason("合金车辆");

        // 车辆详情
        JSONObject carDetail = new JSONObject();
        carDetail.put("a_Number", reservation.getDriverCardNo());
        carDetail.put("d_Name", "物管部");
        accessInfo.setCarDetail(carDetail.toJSONString());

        accessInfo.setCarMobile(reservation.getDriverMobile());
        accessInfo.setChargeType("短期");

        // 司机列表
        JSONArray driverArray = new JSONArray();
        JSONObject driverInfo = new JSONObject();
        driverInfo.put("pin", StringUtils.isNotBlank(reservation.getDriverCardNo()) ?
                reservation.getDriverCardNo() : "");
        driverArray.add(driverInfo);
        accessInfo.setTrunkDrivers(driverArray.toJSONString());

        // 根据enterDoor生成权限配置
        if (reservation.getEnterDoor() != null) {
            String carRightJson = generateCarRightJson(reservation.getEnterDoor());
            accessInfo.setCarRight(carRightJson);
        }

        return accessInfo;
    }

    /**
     * 转换为AccessInfo对象（删除用）
     */
    private AccessInfo convertToDeleteInfo(MatMgmtAlloyReservation reservation) {
        AccessInfo accessInfo = new AccessInfo();
        accessInfo.setCarSerialNo(reservation.getPushNo());
        accessInfo.setCarNo(reservation.getCarNo());
        Date nowdate = new Date();
        accessInfo.setApplyDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, nowdate));

        return accessInfo;
    }

    /**
     * 根据进门方式生成权限配置JSON
     */
    private String generateCarRightJson(String enterDoor) {
        JSONArray carRightArray = new JSONArray();

        if ("2".equals(enterDoor)) {
            // enterDoor为2：前往三号门，三号门code为3，找寻对应的postTag
            JSONObject gateRight = new JSONObject();
            DoorEnum doorEnum = DoorEnum.getByCode("3");
            if (doorEnum != null) {
                gateRight.put("posTag", doorEnum.getPostag());
            }
            carRightArray.add(gateRight);
        }

        return carRightArray.toJSONString();
    }

    public static void main(String[] args) {
        System.out.println(SnowFlakeUtil.getAlloyReservationNoSnowFlakeId());
    }
}
