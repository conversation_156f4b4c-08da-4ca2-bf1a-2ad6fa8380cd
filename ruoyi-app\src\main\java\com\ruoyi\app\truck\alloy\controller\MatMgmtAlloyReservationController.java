package com.ruoyi.app.truck.alloy.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.ruoyi.app.truck.alloy.domain.MatMgmtAlloyReservation;
import com.ruoyi.app.truck.alloy.dto.AlloyCancelDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyScanDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyVerifyDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyAssignDTO;
import com.ruoyi.app.truck.alloy.enums.MatAlloyType;
import com.ruoyi.app.truck.alloy.service.IMatMgmtAlloyReservationService;
import com.ruoyi.app.v1.controller.AppBaseV1Controller;
import com.ruoyi.app.v1.mapper.CompanyUserMapper;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 物管合金车辆预约Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/app/material/alloy")
public class MatMgmtAlloyReservationController extends AppBaseV1Controller {
    private static final Logger log = LoggerFactory.getLogger(MatMgmtAlloyReservationController.class);

    // 合金-采购中心
    public static final String PURCHASE_ROLE_KEY = "matMgmt.alloy.purchase";

    @Autowired
    private IMatMgmtAlloyReservationService matMgmtAlloyReservationService;

    @Autowired
    private CompanyUserMapper companyUserMapper;

    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 获取合金种类
     */
    @GetMapping("/getAlloyType")
    public AjaxResult getAlloyType() {
        return AjaxResult.success(MatAlloyType.getAllAlloyInfo());
    }

    /**
     * 新增预约
     */
    @Log(title = "物管合金车辆预约", businessType = BusinessType.INSERT)
    @PostMapping("/reserve")
    public AjaxResult reserve(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        try {
            String openId = this.getOpenId();
            if (StringUtils.isBlank(openId)) {
                log.error("合金预约新增请求失败 - 无法获取用户OpenId");
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            int result = matMgmtAlloyReservationService.insertMatMgmtAlloyReservationBusiness(matMgmtAlloyReservation, openId);
            if (result > 0) {
                return AjaxResult.success("预约申请提交成功，预约编号：" + matMgmtAlloyReservation.getReservationNo());
            } else {
                return AjaxResult.error("预约申请提交失败，请稍后重试");
            }
        } catch (RuntimeException e) {
            log.error("合金预约新增请求失败：{}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("合金预约新增请求发生异常", e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 审核预约
     */
    @Log(title = "审核合金预约单", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(AlloyVerifyDTO verifyDTO) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String reservationNo = verifyDTO.getReservationNo();
            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            Boolean approveFlag = verifyDTO.getApproveFlag();
            if (approveFlag == null) {
                return AjaxResult.error("审核标志不能为空");
            }

            // 设置工号到DTO中
            verifyDTO.setWorkNo(workNo);
            verifyDTO.setWorkName(getWorkName(workNo));
            int result = matMgmtAlloyReservationService.approveReservation(verifyDTO);

            if (result > 0) {
                return AjaxResult.success("审核成功");
            } else {
                return AjaxResult.error("审核失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            log.error("审核预约失败：{}", e.getMessage());
            return AjaxResult.error("审核异常");
        } catch (Exception e) {
            log.error("审核预约发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 取消申请
     */
    @PostMapping("/cancel")
    public AjaxResult cancel(AlloyCancelDTO cancelDTO) {
        try {
            String openId = this.getOpenId();
            if (StringUtils.isBlank(openId)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String reservationNo = cancelDTO.getReservationNo();
            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            int result = matMgmtAlloyReservationService.cancelReservation(reservationNo, openId);

            if (result > 0) {
                return AjaxResult.success("取消申请成功");
            } else {
                return AjaxResult.error("取消申请失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("取消申请发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 查询预约列表
     */
    @GetMapping("/listForMiniApp")
    public TableDataInfo historyList(MatMgmtAlloyReservation query) {
        TableDataInfo dataInfo = new TableDataInfo();
        try {
            String workNo = this.getWorkNo();
            // 1. 若工号为空，则查询所在供应商预约列表
            if (StringUtils.isBlank(workNo)) {
                // 设置查询条件为当前供应商
                if (StringUtils.isBlank(query.getApplyCompanyId())) {
                    throw new RuntimeException("客商编号查询异常");
                } else {
                    query.setApplyCompanyId(query.getApplyCompanyId());
                }
            } else {
                // 2. 若存在工号，判断角色
                // 判断是否为合金管理相关角色
                boolean hasAdminRole = matMgmtAlloyReservationService.hasAlloyAdminRole(workNo);
                if (hasAdminRole) {
                    // 管理角色可以查询全部预约
                    query.setApplyCompanyId(null);
                } else {
                    // 非管理角色查询为空
                    dataInfo.setTotal(0);
                    dataInfo.setRows(Lists.newArrayList());
                    dataInfo.setCode(HttpStatus.SUCCESS);
                    dataInfo.setMsg("查询成功");
                    return getDataTable(dataInfo, pageNum, pageSize);
                }
            }

            startPage();
            dataInfo = matMgmtAlloyReservationService.selectMatMgmtAlloyReservationTableList(query);
            return getDataTable(dataInfo, pageNum, pageSize);

        } catch (Exception e) {
            log.error("合金预约列表查询发生异常", e);
            throw new RuntimeException("预约列表查询异常");
        }
    }

    /**
     * 待审核列表
     */
    @GetMapping("/pendingListForMiniApp")
    public TableDataInfo pendingListForMiniApp(MatMgmtAlloyReservation query) {
        TableDataInfo dataInfo = new TableDataInfo();
        try {
            String workNo = this.getWorkNo();
            // 1. 若工号为空，则查询所在供应商预约列表
            if (StringUtils.isBlank(workNo)) {
                throw new RuntimeException("预约列表查询异常");
            } else {
                // 2. 判断是否是采购中心审核人
                boolean hasPurchaseRole = sysRoleService.selectRoleExistByUserName(workNo, PURCHASE_ROLE_KEY);
                if (hasPurchaseRole) {
                    List<String> reservationStatusList = Arrays.asList("1", "22");
                    query.setReservationStatusList(reservationStatusList);
                } else {
                    dataInfo.setTotal(0);
                    dataInfo.setRows(Lists.newArrayList());
                    dataInfo.setCode(HttpStatus.SUCCESS);
                    dataInfo.setMsg("查询成功");
                    return getDataTable(dataInfo, pageNum, pageSize);
                }
            }

            startPage();
            dataInfo = matMgmtAlloyReservationService.selectMatMgmtAlloyReservationTableList(query);
            return getDataTable(dataInfo, pageNum, pageSize);

        } catch (Exception e) {
            log.error("合金预约列表查询发生异常", e);
            throw new RuntimeException("预约列表查询异常");
        }
    }


    /**
     * 获取预约详情
     */
    @GetMapping("/detail")
    public AjaxResult getDetail(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        try {
            String reservationNo = matMgmtAlloyReservation.getReservationNo();
            if (StringUtils.isBlank(reservationNo)) {
                log.error("获取预约详情失败 - 预约号为空");
                return AjaxResult.error("未获取预约号");
            }

            String workNo = this.getWorkNo();
            Map<String, Object> detail = matMgmtAlloyReservationService.getReservationDetail(reservationNo, workNo);

            Boolean success = (Boolean) detail.get("success");
            if (success != null && success) {
                return AjaxResult.success(detail);
            } else {
                String message = (String) detail.get("message");
                return AjaxResult.error(message != null ? message : "查询失败");
            }

        } catch (Exception e) {
            log.error("获取预约详情发生异常", e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

//    /**
//     * 合金扫码操作（直接修改、暂不使用）
//     */
//    @PostMapping("/alloyScanCode")
//    public AjaxResult alloyScanCode(AlloyScanDTO scanDTO) {
//        try {
//            String reservationNo = scanDTO.getReservationNo();
//            if (StringUtils.isBlank(reservationNo)) {
//                return AjaxResult.error("预约编号不能为空");
//            }
//
//            String scanAction = scanDTO.getScanAction();
//            if (StringUtils.isBlank(scanAction)) {
//                return AjaxResult.error("扫码动作不能为空");
//            }
//
//            String workNo = this.getWorkNo();
//            String workName = this.getWorkName(workNo);
//
//            // 执行扫码操作
//            Map<String, Object> result = matMgmtAlloyReservationService.alloyScanCode(reservationNo, scanAction, workNo, workName);
//
//            Boolean success = (Boolean) result.get("success");
//            String message = (String) result.get("message");
//
//            // 返回结果
//            if (success != null && success) {
//                return AjaxResult.success(message);
//            } else {
//                return AjaxResult.error(message != null ? message : "扫码异常");
//            }
//
//        } catch (Exception e) {
//            log.error("合金扫码操作发生异常", e);
//            return AjaxResult.error("系统异常");
//        }
//    }

    /**
     * 物管分配
     */
    @PostMapping("/materialAssign")
    public AjaxResult materialAssign(AlloyAssignDTO assignDTO) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String workName = this.getWorkName(workNo);

            if (StringUtils.isBlank(assignDTO.getReservationNo())) {
                return AjaxResult.error("预约编号不能为空");
            }

            if (assignDTO.getEffectiveStartTime() == null || assignDTO.getEffectiveEndTime() == null) {
                return AjaxResult.error("有效时间不能为空");
            }

            if (assignDTO.getEnterDoor() == null) {
                return AjaxResult.error("大门不能为空");
            }

            int result = matMgmtAlloyReservationService.materialAssign(assignDTO, workNo, workName);

            if (result > 0) {
                return AjaxResult.success("分配成功");
            } else {
                return AjaxResult.error("分配失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("物管分配发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 重新分配
     */
    @PostMapping("/materialReassign")
    public AjaxResult materialReassign(AlloyAssignDTO assignDTO) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String workName = this.getWorkName(workNo);

            if (StringUtils.isBlank(assignDTO.getReservationNo())) {
                return AjaxResult.error("预约编号不能为空");
            }

            if (assignDTO.getEffectiveStartTime() == null || assignDTO.getEffectiveEndTime() == null) {
                return AjaxResult.error("有效时间不能为空");
            }

            if (assignDTO.getEnterDoor() == null) {
                return AjaxResult.error("大门不能为空");
            }

            int result = matMgmtAlloyReservationService.materialReassign(assignDTO, workNo, workName);

            if (result > 0) {
                return AjaxResult.success("重新分配成功");
            } else {
                return AjaxResult.error("重新分配失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("重新分配发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 物管确认
     */
    @PostMapping("/materialConfirm")
    public AjaxResult materialConfirm(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String workName = this.getWorkName(workNo);
            String reservationNo = matMgmtAlloyReservation.getReservationNo();

            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            int result = matMgmtAlloyReservationService.materialConfirm(reservationNo, workNo, workName);

            if (result > 0) {
                return AjaxResult.success("确认成功");
            } else {
                return AjaxResult.error("确认失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("物管确认发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 签到
     */
    @PostMapping("/checkInNew")
    public AjaxResult checkInNew(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String workName = this.getWorkName(workNo);
            String reservationNo = matMgmtAlloyReservation.getReservationNo();

            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            int result = matMgmtAlloyReservationService.checkIn(reservationNo, workNo, workName);

            if (result > 0) {
                return AjaxResult.success("签到成功");
            } else {
                return AjaxResult.error("签到失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("签到发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 门卫确认通行（安全村）
     */
    @PostMapping("/entranceGuardScan")
    public AjaxResult entranceGuardScan(MatMgmtAlloyReservation matMgmtAlloyReservation) {
        try {
            String workNo = this.getWorkNo();
            if (StringUtils.isBlank(workNo)) {
                return AjaxResult.error("用户信息异常，请重新登录");
            }

            String workName = this.getWorkName(workNo);
            String reservationNo = matMgmtAlloyReservation.getReservationNo();

            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            int result = matMgmtAlloyReservationService.entranceGuardScan(reservationNo, workNo, workName);

            if (result > 0) {
                return AjaxResult.success("确认通行成功");
            } else {
                return AjaxResult.error("确认通行失败，请稍后重试");
            }

        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("确认通行发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

    /**
     * 扫码检查（只检查状态，不修改数据）
     */
    @PostMapping("/scanCheck")
    public AjaxResult scanCheck(AlloyScanDTO scanDTO) {
        try {
            String workNo = this.getWorkNo();
            String workName = this.getWorkName(workNo);

            String reservationNo = scanDTO.getReservationNo();
            String scanAction = scanDTO.getScanAction();

            if (StringUtils.isBlank(reservationNo)) {
                return AjaxResult.error("预约编号不能为空");
            }

            if (StringUtils.isBlank(scanAction)) {
                return AjaxResult.error("扫码动作不能为空");
            }

            Map<String, Object> result = matMgmtAlloyReservationService.scanCheck(reservationNo, scanAction, workNo, workName);

            Boolean success = (Boolean) result.get("success");
            String message = (String) result.get("message");

            if (success != null && success) {
                return AjaxResult.success(message);
            } else {
                return AjaxResult.error(message != null ? message : "检查失败");
            }

        } catch (Exception e) {
            log.error("扫码检查发生异常", e);
            return AjaxResult.error("系统异常");
        }
    }

}
