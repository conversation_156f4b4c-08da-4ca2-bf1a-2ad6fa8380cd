package com.ruoyi.web.controller.xctg;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HrDept;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.stream.Collectors;

/**
 * TYjyFormController
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/web/TYjy/form")
public class WebTYjyFormController extends BaseController {
    @Autowired
    private ITYjyFormService tYjyFormService;

    @Autowired
    private ISysDictTypeService sysDictTypeService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ITYjyDeptUserService tYjyDeptUserService;

    @Autowired
    private ITYjyPermissionService tYjyPermissionService;
    @Autowired
    private ITYjyAnswerService tYjyAnswerService;

    @Autowired
    private ITYjyDimensionalityService tYjyDimensionalityService;
    /**
     * 查询TYjyForm列表
     */

    @Autowired
    private ITYjyFormHistoryService tYjyFormHistoryService;


    @GetMapping("/list")
    public TableDataInfo list(TYjyForm tYjyForm) {

        String workNo=SecurityUtils.getUsername();
//        TYjyDeptUser tYjyDeptUser=new TYjyDeptUser();
//        tYjyDeptUser.setWorkNo(workNo);
//        tYjyDeptUser.setRuleType("100");
//        List<TYjyDeptUser> searchrole = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
//        if(searchrole.size()==0)
//        {
//            TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
//            tYjyDimensionality.setCreateBy(workNo);
//            List<TYjyDimensionality> dimensionalityList = tYjyDimensionalityService.selectRootListWithDept(tYjyDimensionality);
//            List<Long> dimensionalityIds = new ArrayList<>();
//            for(TYjyDimensionality item:dimensionalityList)
//            {
//                dimensionalityIds.add(item.getId());
//            }
//            tYjyForm.setDimensionalityIds(dimensionalityIds);
//            startPage();
//            List<TYjyForm> list = tYjyFormService.selectTYjyFormListWithDimensionality(tYjyForm);
//            return getDataTable(list);
//        }
//        else
//        {
//            startPage();
//            List<TYjyForm> list = tYjyFormService.selectTYjyFormList(tYjyForm);
//            return getDataTable(list);
//        }
        tYjyForm.setWorkNo(workNo);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectTYjyFormListWithDimensionality(tYjyForm);
        return getDataTable(list);
    }


    @GetMapping("/unadminlist")
    public TableDataInfo unadminlist(TYjyForm tYjyForm) {
        String workNo=SecurityUtils.getUsername();
        tYjyForm.setWorkNo(workNo);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectFormListUnadmin(tYjyForm);
        return getDataTable(list);
    }

    @GetMapping("/unsubmitlist")
    public TableDataInfo unsubmitlist(TYjyForm tYjyForm) {

        String workNo=SecurityUtils.getUsername();
        tYjyForm.setWorkNo(workNo);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectFormListUnsubmit(tYjyForm);
        return getDataTable(list);
    }


    @GetMapping("/triggerlist")
    public TableDataInfo triggerlist(TYjyForm tYjyForm) {
        String workNo=SecurityUtils.getUsername();
        tYjyForm.setWorkNo(workNo);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectTYjyFormListTriggerlist(tYjyForm);
        return getDataTable(list);
    }

    @GetMapping("/triggerlistWithNote")
    public TableDataInfo triggerlistWithNote(TYjyForm tYjyForm) {
        String workNo=SecurityUtils.getUsername();
        tYjyForm.setWorkNo(workNo);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectTYjyFormListTriggerlistWithNote(tYjyForm);
        return getDataTable(list);
    }


    @GetMapping("/distributelist")
    public TableDataInfo distributelist(TYjyForm tYjyForm) {
        tYjyForm.setWorkNo(SecurityUtils.getUsername());
        startPage();
        List<TYjyForm> list = tYjyFormService.selectForDistribute(tYjyForm);
        return getDataTable( tYjyFormService.dealdistributelist(list));
    }


    @GetMapping("/deptList")
    public AjaxResult list() {
        List<HrDept> list = deptService.selectHrDeptList(new HrDept());
        return AjaxResult.success(list);
    }



//    @GetMapping("/answerlist")
//    public AjaxResult answerlist(TYjyForm tYjyForm) {
//
////        List<SysDictData> frequency = sysDictTypeService.selectDictDataByType("data_report_form_frequency");
////        List<SysDictData> formType = sysDictTypeService.selectDictDataByType("data-report-form-type");
////        Map<String, String> typeMap = new HashMap<>();
////        for (SysDictData item : formType) {
////            typeMap.put(item.getDictValue(), item.getDictLabel());
////        }
//        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(currentDate); //
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
////        if(tYjyForm.getDeptCode()==null)
////        {
////
////            SysUser user= SecurityUtils.getLoginUser().getUser();
////            if(user.getRsDeptCode()==null)
////            {
////                return AjaxResult.error("填报用户部门编号不能为空，请联系智信部修正用户信息");
////            }
////            else
////            {
////                tYjyForm.setDeptCode(user.getRsDeptCode());
////            }
////        }
//        List<TYjyForm> list = tYjyFormService.selectTYjyFormListForAnswer(tYjyForm);
//        List<Long> dimensionalityList = list.stream().map(x -> x.getDimensionalityId()).distinct().collect(Collectors.toList());
//
//        Map<Long, List<TYjyForm>> groupedByQuestion = list.stream()
//                .collect(Collectors.groupingBy(TYjyForm::getDimensionalityId));
//
//        TYjyFormDetail relist = new TYjyFormDetail();
//        dimensionalityList.forEach(dimensionality -> {
//            JSONObject item = new JSONObject();
//            item.put("span", 24);
//            item.put("vertical", true);
//            item.put("titleBold", true);
//            item.put("title", groupedByQuestion.get(dimensionality).get(0).getDimensionalityNames());
//            JSONArray child = new JSONArray();
//            List<TYjyForm> forms = groupedByQuestion.get(dimensionality);
//            forms.forEach(form -> {
//                JSONObject answer = new JSONObject();
//                answer.put("field", "data-" + form.getId());
//
//                TYjyAnswer tYjyAnswer=new TYjyAnswer();
//                tYjyAnswer.setFormId(form.getId());
////                tYjyAnswer.setCreatorDept(tYjyForm.getDeptCode());
//                if(form.getFrequency().equals("0"))//时间上的判断还需要进一步的考虑，还有进一步确定要获取的范围
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth+1)+"-"+(currentDay));
//                }else if(form.getFrequency().equals("1"))
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth+1)+"-"+1);
//                }else if(form.getFrequency().equals("2"))
//                {
//                    if(currentMonth<=2)
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+1+"-1");
//                    }else if(currentMonth<=5)
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+4+"-1");
//                    }else if(currentMonth<=8)
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+7+"-1");
//                    }else if(currentMonth<=11)
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+10+"-1");
//                    }
//
//                }else if(form.getFrequency().equals("3"))
//                {
//                    if(currentMonth<=5)
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+1+"-1");
//                    }
//                    else
//                    {
//                        tYjyAnswer.setFcDate(currentYear+"-"+7+"-1");
//                    }
//                }else if(form.getFrequency().equals("4"))
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-"+1+"-1");
//                }
//
//                List<TYjyAnswer> answerlist =tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer);
//                if(answerlist.size()>0)
//                {
//                    if(form.getFormType().equals("0"))
//                    {
//                        relist.getData().put("data-" + form.getId(),Integer.valueOf(tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer).get(0).getFormValue()));//此处填充数据,把查询到的历史记录填入其中
//                    }
//                    if(form.getFormType().equals("1"))
//                    {
//                        relist.getData().put("data-" + form.getId(),Double.valueOf(tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer).get(0).getFormValue()));//此处填充数据,把查询到的历史记录填入其中
//                    }
//                    if(form.getFormType().equals("2")||form.getFormType().equals("3"))
//                    {
//                        relist.getData().put("data-" + form.getId(),tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer).get(0).getFormValue());//此处填充数据,把查询到的历史记录填入其中
//                    }
//                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                    {
//                        relist.getData().put("data-" + form.getId(),JSONObject.parse(tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer).get(0).getFormValue()));
//                    }
//                }
//                else
//                {
//                    if (form.getFormType().equals("4")||form.getFormType().equals("5"))
//                    {
//                        relist.getData().put("data-" + form.getId(), new ArrayList());
//                    }
//                    else
//                    {
//                        relist.getData().put("data-" + form.getId(), null);
//                    }
//                }
////                //此处填充数据,把查询到的历史记录填入其中
//                answer.put("title", form.getFormQuestion());
//                answer.put("formType", form.getFormType());
//                answer.put("span", 20);
//                if (form.getFormType().equals("0")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "   \"name\": \"VxeInput\", \"props\": { \"type\": \"integer\"," +
//                            "   \"clearable\": true \n" +
//                            " }\n" +
//                            "}"));
//                }
//                if (form.getFormType().equals("1")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "   \"name\": \"VxeInput\", \"props\": { \"type\": \"float\"," +
//                            "   \"clearable\": true, \n" +
//                            "   \"digits\": 3 \n" +
//                            "}\n" +
//                            "}"));
//                }
//
//                if (form.getFormType().equals("2")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "   \"name\": \"VxeInput\", \"props\": { \"clearable\": true \n" +
//                            "}\n" +
//                            "}"));
//                }
//                if (form.getFormType().equals("3")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "   \"name\": \"VxeTextarea\", \n" +
//                            "   \"props\": {\n" +
//                            "   \"clearable\": true, \n" +
//                            "     \"autosize\": {\n" +
//                            "       \"minRows\":3,\n" +
//                            "       \"maxRows\":6\n" +
//                            "     }\n" +
//                            "   }\n" +
//                            "}"));
//                }
//                if (form.getFormType().equals("4")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "\"name\":\"VxeUpload\",\n" +
//                            "\"props\":{\n" +
//                            "\"multiple\":true\n" +
//                            "}}"));
////                    if(answerlist.size()==0)
////                    {
////                        relist.getData().put("data-" + form.getId(), new ArrayList());
////                    }
//                }
//                if (form.getFormType().equals("5")) {
//                    answer.put("itemRender", JSONObject.parse("{\n" +
//                            "\"name\":\"VxeUpload\",\n" +
//                            "\"props\":{\n" +
//                            "\"multiple\":true,\n" +
//                            "\"mode\":\"image\"\n" +
//                            "}}"));
////                    if(answerlist.size()==0)
////                    {
////                        relist.getData().put("data-" + form.getId(), new ArrayList());
////                    }
//                }
//                child.add(answer);
//            });
//
//            item.put("children", child);
//            relist.getItems().add(item);
//
//        });
//        return AjaxResult.success(relist);
//    }

//    @GetMapping("/answerlistplus")
//    public AjaxResult answerlistplus(TYjyForm tYjyForm) {
//        // 创建Calendar实例，并设置为当前时间
//        Calendar calendar = Calendar.getInstance();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        if(tYjyForm.getFcDate()!=null)
//        {
//            try {
//                Date currentDate = dateFormat.parse(tYjyForm.getFcDate());
//                calendar.setTime(currentDate);
//            } catch (ParseException e) {
//                // 异常处理代码
//                e.printStackTrace();
//                // 或者其他错误处理逻辑
//            }
//        }
//        else
//        {
//            Date currentDate = new Date();
//            calendar.setTime(currentDate);
//        }
//
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//        Calendar changetime = Calendar.getInstance();
//        changetime.set(currentYear,currentMonth,currentDay);
//        String fcDate = dateFormat.format(changetime.getTime());
//        changetime.add(Calendar.DAY_OF_YEAR,-3);
//        String changefcDate = dateFormat.format(changetime.getTime());
//        String workNo=SecurityUtils.getUsername();
//        tYjyForm.setWorkNo(workNo);
//
////        startPage();
//        List<TYjyForm> list = tYjyFormService.selectTYjyFormListForAnswer(tYjyForm);
//
//        Map<String,List<TYjyForm>> answermap= new HashMap<>();
//        Map<Long,List<TYjyAnswer>> answerhistory= new HashMap<>();
//        TYjyAnswer tYjyAnswer=new TYjyAnswer();
//        //***  根据维度来决定部门,填报权限的进一步细化，获取的内容也细化了,需要精细化到填报人
//        // tYjyAnswer.setCreatorDept(tYjyForm.getDeptCode());
//        tYjyAnswer.setWorkNo(workNo);
//        tYjyAnswer.setFcDate(fcDate);
//        List<TYjyAnswer> answerlist =tYjyAnswerService.selectAnswerTop3(tYjyAnswer);//这个方法需要进一步的优化查询效率
//        for(TYjyAnswer item:answerlist)
//        {
//            if(answerhistory.containsKey(item.getFormId()))
//            {
//                answerhistory.get(item.getFormId()).add(item);
//            }
//            else
//            {
//                List<TYjyAnswer> insert=new ArrayList<>();
//                insert.add(item);
//                answerhistory.put(item.getFormId(),insert);
//            }
//        }
//        list.forEach(form -> {
//            if(answermap.containsKey(form.getCreatorDeptCode()))
//            {
//                answermap.get(form.getCreatorDeptCode()).add(form);
//            }
//            else
//            {
//                List<TYjyForm> insert=new ArrayList<>();
//                insert.add(form);
//                answermap.put(form.getCreatorDeptCode(),insert);
//            }
//        }
//        );
//        List<JSONObject> result= new ArrayList<>();
//        List<TYjyAnswer> deallist;
//        String nowtime=currentYear+"-"+(currentMonth+1)+"-"+(currentDay);
//        for(String item:answermap.keySet())
//        {
//            JSONObject json = new JSONObject();
//            List<TYjyForm> deal=answermap.get(item);
//            json.put("deptCode",deal.get(0).getCreatorDeptCode());
//            json.put("deptName",deal.get(0).getCreatorDeptName());
//            List<JSONObject> array=new ArrayList<>();
//            for(TYjyForm form:deal)
//            {
//                if(form.getFrequency().equals("0"))//
//                {
//                    nowtime=currentYear+"";
//                    if(currentMonth<9)
//                    {
//                        nowtime=nowtime+"-0"+(currentMonth+1);
//                    }
//                    else
//                    {
//                        nowtime=nowtime+"-"+(currentMonth+1);
//                    }
//                    if(currentDay<10)
//                    {
//                        nowtime=nowtime+"-0"+currentDay;
//                    }
//                    else
//                    {
//                        nowtime=nowtime+"-"+currentDay;
//                    }
//                }else if(form.getFrequency().equals("1"))
//                {
//                    if(currentMonth<9)
//                    {
//                        nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
//                    }
//                    else
//                    {
//                        nowtime=currentYear+"-"+(currentMonth+1)+"-01";
//                    }
//                }else if(form.getFrequency().equals("2"))
//                {
//                    if(currentMonth<=2)
//                    {
//                        nowtime=currentYear+"-01-01";
//                    }else if(currentMonth<=5)
//                    {
//                        nowtime=currentYear+"-04-01";
//                    }else if(currentMonth<=8)
//                    {
//                        nowtime=currentYear+"-07-01";
//                    }else if(currentMonth<=11)
//                    {
//                        nowtime=currentYear+"-10-01";
//                    }
//
//                }else if(form.getFrequency().equals("3"))
//                {
//                    if(currentMonth<=5)
//                    {
//                        nowtime=currentYear+"-01-01";
//                    }
//                    else
//                    {
//                        nowtime=currentYear+"-07-01";
//                    }
//                }else if(form.getFrequency().equals("4"))
//                {
//                    nowtime=currentYear+"-01-01";
//                }
//
//                JSONObject jsonResult=new JSONObject();
//                JSONObject hisData=new JSONObject();
//                JSONObject hisDatain=new JSONObject();
//                jsonResult.put("formId",form.getId());
//                jsonResult.put("formQuestion",form.getFormQuestion());
//                jsonResult.put("formType",form.getFormType());
//                jsonResult.put("creatorDept",form.getDeptCode());
//                int count=0;
//                int num=0;
//                deallist=answerhistory.get(form.getId());
//                if(deallist==null)
//                {
//                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                    {
//                        jsonResult.put("formValue",new ArrayList());
//                    }
//                    else
//                    {
//                        jsonResult.put("formValue",null);
//                    }
//                    jsonResult.put("status","4");
//                    jsonResult.put("checkHistory","");
//                    while(num<3)
//                    {
//                        hisDatain=new JSONObject();
//                        hisDatain.put("title",null);
//                        if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                        {
//                            hisDatain.put("formValue",new ArrayList());
//                        }
//                        else
//                        {
//                            hisDatain.put("formValue",null);
//                        }
//
//                        hisDatain.put("status","4");
//                        hisDatain.put("checkHistory","");
//                        hisData.put("data"+(num+1),hisDatain);
//                        num=num+1;
//                    }
//                }
//                else
//                {
//                    while(count<=3&&count<deallist.size()&&num<3)
//                    {
//                        if(count==0)
//                        {
//                            if(deallist.get(count).getFcDate().compareTo(nowtime)>=0)
//                            {
//                                if(deallist.get(count).getFormValue()==null)
//                                {
//                                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                    {
//                                        jsonResult.put("formValue",new ArrayList());
//                                    }
//                                    else
//                                    {
//                                        jsonResult.put("formValue",null);
//                                    }
//                                }
//                                else
//                                {
//                                    if(form.getFormType().equals("0"))
//                                    {
//                                        jsonResult.put("formValue",Integer.valueOf(deallist.get(count).getFormValue()));
//                                    }
//                                    if(form.getFormType().equals("1"))
//                                    {
//                                        jsonResult.put("formValue",Double.valueOf(deallist.get(count).getFormValue()));
//
//                                    }
//                                    if(form.getFormType().equals("2")||form.getFormType().equals("3"))
//                                    {
//                                        jsonResult.put("formValue",deallist.get(count).getFormValue());
//                                    }
//                                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                    {
//                                        jsonResult.put("formValue",JSONObject.parse(deallist.get(count).getFormValue()));
//                                    }
//                                }
//                                jsonResult.put("status",deallist.get(count).getStatus());
//                                jsonResult.put("checkHistory",deallist.get(count).getCheckHistory());
//                                count=count+1;
//                            }
//                            else
//                            {
//
//                                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                {
//                                    jsonResult.put("formValue",new ArrayList());
//                                }
//                                else
//                                {
//                                    jsonResult.put("formValue",null);
//                                }
//                                jsonResult.put("status","4");
//                                jsonResult.put("checkHistory","");
//                                hisDatain=new JSONObject();
//                                hisDatain.put("title",deallist.get(count).getFcDate()+"("+deallist.get(count).getCreatorName()+")");
//                                if(deallist.get(count).getFormValue()==null)
//                                {
//                                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                    {
//                                        hisDatain.put("formValue",new ArrayList());
//                                    }
//                                    else
//                                    {
//                                        hisDatain.put("formValue",null);
//                                    }
//                                }
//                                else
//                                {
//                                    if(form.getFormType().equals("0"))
//                                    {
//                                        hisDatain.put("value",Integer.valueOf(deallist.get(count).getFormValue()));
//                                    }
//                                    if(form.getFormType().equals("1"))
//                                    {
//                                        hisDatain.put("value",Double.valueOf(deallist.get(count).getFormValue()));
//                                    }
//                                    if(form.getFormType().equals("2")||form.getFormType().equals("3"))
//                                    {
//                                        hisDatain.put("value",deallist.get(count).getFormValue());
//                                    }
//                                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                    {
//                                        hisDatain.put("value",JSONObject.parse(deallist.get(count).getFormValue()));
//                                    }
//                                }
//                                hisDatain.put("status",deallist.get(count).getStatus());
//                                hisDatain.put("checkHistory",deallist.get(count).getCheckHistory());
//                                hisData.put("data"+(num+1),hisDatain);
//                                count=count+1;
//                                num=num+1;
//                            }
//                        }
//                        else
//                        {
//                            hisDatain=new JSONObject();
//                            hisDatain.put("title",deallist.get(count).getFcDate()+"("+deallist.get(count).getCreatorName()+")");
//                            if(deallist.get(count).getFormValue()==null)
//                            {
//                                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                {
//                                    hisDatain.put("formValue",new ArrayList());
//                                }
//                                else
//                                {
//                                    hisDatain.put("formValue",null);
//                                }
//                            }
//                            else
//                            {
//                                if(form.getFormType().equals("0"))
//                                {
//                                    hisDatain.put("value",Integer.valueOf(deallist.get(count).getFormValue()));
//                                }
//                                if(form.getFormType().equals("1"))
//                                {
//                                    hisDatain.put("value",Double.valueOf(deallist.get(count).getFormValue()));
//                                }
//                                if(form.getFormType().equals("2")||form.getFormType().equals("3"))
//                                {
//                                    hisDatain.put("value",deallist.get(count).getFormValue());
//                                }
//                                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                                {
//                                    hisDatain.put("value",JSONObject.parse(deallist.get(count).getFormValue()));
//                                }
//                            }
//                            hisDatain.put("status",deallist.get(count).getStatus());
//                            hisDatain.put("checkHistory",deallist.get(count).getCheckHistory());
//                            hisData.put("data"+(num+1),hisDatain);
//                            num=num+1;
//                            count=count+1;
//                        }
//                    }
//                    while(count<=3&&num<3)
//                    {
//                        hisDatain=new JSONObject();
//                        hisDatain.put("title",null);
//                        if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                        {
//                            hisDatain.put("formValue",new ArrayList());
//                        }
//                        else
//                        {
//                            hisDatain.put("formValue",null);
//                        }
//                        hisDatain.put("status","4");
//                        hisDatain.put("checkHistory","");
//                        hisData.put("data"+(num+1),hisDatain);
//                        num=num+1;
//                        count=count+1;
//                    }
//                }
//                jsonResult.put("hisData",hisData);
//                array.add(jsonResult);
//            }
//            json.put("data",array);
//            result.add(json);
//        }
//        return AjaxResult.success(result);
//    }


    @GetMapping("/answerlistplusplus")
    public AjaxResult answerlistplusplus(TYjyForm tYjyForm) {

        if(tYjyForm.getFrequency().equals("9"))
        {
            tYjyForm.setFrequency(null);
        }

        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(tYjyForm.getFcDate()!=null)
        {
            try {
                Date currentDate = dateFormat.parse(tYjyForm.getFcDate());
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        else
        {
            Date currentDate = new Date();
            calendar.setTime(currentDate);
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        Calendar changetime = Calendar.getInstance();
        changetime.set(currentYear,currentMonth,currentDay);
        String fcDate = dateFormat.format(changetime.getTime());
        changetime.add(Calendar.DAY_OF_YEAR,-3);
        String workNo=SecurityUtils.getUsername();
        tYjyForm.setWorkNo(workNo);
        int total=tYjyFormService.selectTYjyFormListForAnswerCount(tYjyForm);
        startPage();
        List<TYjyForm> list = tYjyFormService.selectTYjyFormListForAnswer(tYjyForm);
        List<JSONObject> result= new ArrayList<>();
        List<TYjyAnswer> deallist;
        String nowtime=currentYear+"-"+(currentMonth+1)+"-"+(currentDay);
        JSONObject json = new JSONObject();
        List<JSONObject> array=new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyForm form:list)
        {
            if(form.getFrequency().equals("0"))//
            {
                nowtime=currentYear+"";
                if(currentMonth<9)
                {
                    nowtime=nowtime+"-0"+(currentMonth+1);
                }
                else
                {
                    nowtime=nowtime+"-"+(currentMonth+1);
                }
                if(currentDay<10)
                {
                    nowtime=nowtime+"-0"+currentDay;
                }
                else
                {
                    nowtime=nowtime+"-"+currentDay;
                }
            }else if(form.getFrequency().equals("1"))
            {
                if(currentMonth<9)
                {
                    nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
                }
                else
                {
                    nowtime=currentYear+"-"+(currentMonth+1)+"-01";
                }
            }else if(form.getFrequency().equals("2"))
            {
                if(currentMonth<=2)
                {
                    nowtime=currentYear+"-01-01";
                }else if(currentMonth<=5)
                {
                    nowtime=currentYear+"-04-01";
                }else if(currentMonth<=8)
                {
                    nowtime=currentYear+"-07-01";
                }else if(currentMonth<=11)
                {
                    nowtime=currentYear+"-10-01";
                }

            }else if(form.getFrequency().equals("3"))
            {
                if(currentMonth<=5)
                {
                    nowtime=currentYear+"-01-01";
                }
                else
                {
                    nowtime=currentYear+"-07-01";
                }
            }else if(form.getFrequency().equals("4"))
            {
                nowtime=currentYear+"-01-01";
            }else if(form.getFrequency().equals("5"))
            {

//                Calendar weekcalendar = Calendar.getInstance();
//                int test=calendar.get(Calendar.DAY_OF_WEEK);
//                if(calendar.get(Calendar.DAY_OF_WEEK)<=1)
//                {
//                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH)-1, 2);
//                }
//                else
//                {
//                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH), 2);
//                }
//                nowtime=dateFormat.format(weekcalendar.getTime());
                calendar.add(Calendar.DAY_OF_YEAR,-1);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                nowtime=dateFormat.format(calendar.getTime());
            }

            JSONObject jsonResult=new JSONObject();
            JSONObject hisData=new JSONObject();
            JSONObject hisDatain=new JSONObject();
            jsonResult.put("formId",form.getId());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("formType",form.getFormType());
            jsonResult.put("minimum",form.getMinimum());
            jsonResult.put("maximum",form.getMaximum());
            jsonResult.put("creatorDept",form.getDeptCode());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityPath(),dimensionalityMap));
            jsonResult.put("formNote",form.getFormNote());
            jsonResult.put("formNote1",form.getFormNote1());
            jsonResult.put("formUnit",form.getUnit());
            int count=0;
            int num=0;
            TYjyAnswer tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setWorkNo(workNo);
            tYjyAnswer.setFcDate(nowtime);
            //填报部门的石山
//            tYjyAnswer.setCreatorDept(form.getDeptCode());
            tYjyAnswer.setFormId(form.getId());
            deallist =tYjyAnswerService.selectAnswerTop4(tYjyAnswer);

//            deallist=answerhistory.get(form.getId()+" "+form.getDeptCode());
            if(deallist.size()==0)
            {
                jsonResult.put("formValue",null);
                jsonResult.put("formFile",new ArrayList());
                jsonResult.put("status","4");
                jsonResult.put("checkHistory","");
                while(num<3)
                {
                    hisDatain=new JSONObject();
                    hisDatain.put("title",null);
                    jsonResult.put("formValue",null);
                    jsonResult.put("formFile",new ArrayList());
                    hisDatain.put("status","4");
                    hisDatain.put("checkHistory","");
                    hisData.put("data"+(num+1),hisDatain);
                    num=num+1;
                }
            }
            else
            {
                while(count<=3&&count<deallist.size()&&num<3)
                {
                    if(count==0)
                    {
                        if(deallist.get(count).getFcDate().compareTo(nowtime)>=0)
                        {
                            if(deallist.get(count).getFormValue()==null)
                            {

                                jsonResult.put("formValue",null);
                            }
                            else
                            {
                                if(form.getFormType().equals("0"))
                                {
                                    jsonResult.put("formValue",Integer.valueOf(deallist.get(count).getFormValue()));
                                }
                                if(form.getFormType().equals("1"))
                                {
                                    jsonResult.put("formValue",Double.valueOf(deallist.get(count).getFormValue()));

                                }
                                if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                                {
                                    jsonResult.put("formValue",deallist.get(count).getFormValue());
                                }
                            }
                            if(deallist.get(count).getFormFile()==null)
                            {
                                jsonResult.put("formFile", new ArrayList());
                            }
                            else
                            {
                                jsonResult.put("formFile",JSONObject.parse(deallist.get(count).getFormFile()));
                            }

                            jsonResult.put("status",deallist.get(count).getStatus());
                            jsonResult.put("checkHistory",deallist.get(count).getCheckHistory());
                            count=count+1;
                        }
                        else
                        {
                            jsonResult.put("formValue",null);
                            jsonResult.put("formFile",new ArrayList());
                            jsonResult.put("status","4");
                            jsonResult.put("checkHistory","");
                            hisDatain=new JSONObject();
                            hisDatain.put("title",deallist.get(count).getFcDate()+"("+deallist.get(count).getCreatorName()+")");
                            if(deallist.get(count).getFormValue()==null)
                            {
                                hisDatain.put("formValue",null);
                            }
                            else
                            {
                                if(form.getFormType().equals("0"))
                                {
                                    hisDatain.put("value",Integer.valueOf(deallist.get(count).getFormValue()));
                                }
                                if(form.getFormType().equals("1"))
                                {
                                    hisDatain.put("value",Double.valueOf(deallist.get(count).getFormValue()));
                                }
                                if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                                {
                                    hisDatain.put("value",deallist.get(count).getFormValue());
                                }
                            }
                            hisDatain.put("status",deallist.get(count).getStatus());
                            hisDatain.put("checkHistory",deallist.get(count).getCheckHistory());
                            hisData.put("data"+(num+1),hisDatain);
                            count=count+1;
                            num=num+1;
                        }
                    }
                    else
                    {
                        hisDatain=new JSONObject();
                        hisDatain.put("title",deallist.get(count).getFcDate()+"("+deallist.get(count).getCreatorName()+")");
                        if(deallist.get(count).getFormValue()==null)
                        {
                            hisDatain.put("formValue",null);
                        }
                        else
                        {
                            if(form.getFormType().equals("0"))
                            {
                                hisDatain.put("value",Integer.valueOf(deallist.get(count).getFormValue()));
                            }
                            if(form.getFormType().equals("1"))
                            {
                                hisDatain.put("value",Double.valueOf(deallist.get(count).getFormValue()));
                            }
                            if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                            {
                                hisDatain.put("value",deallist.get(count).getFormValue());
                            }
                        }
                        hisDatain.put("status",deallist.get(count).getStatus());
                        hisDatain.put("checkHistory",deallist.get(count).getCheckHistory());
                        hisData.put("data"+(num+1),hisDatain);
                        num=num+1;
                        count=count+1;
                    }
                }
                while(count<=3&&num<3)
                {
                    hisDatain=new JSONObject();
                    hisDatain.put("title",null);
                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
                    {
                        hisDatain.put("formValue",new ArrayList());
                    }
                    else
                    {
                        hisDatain.put("formValue",null);
                    }
                    hisDatain.put("status","4");
                    hisDatain.put("checkHistory","");
                    hisData.put("data"+(num+1),hisDatain);
                    num=num+1;
                    count=count+1;
                }
            }
            jsonResult.put("hisData",hisData);



            array.add(jsonResult);
        }
        json.put("data",array);
        result.add(json);
        JSONObject re=new JSONObject();
        re.put("data",result);
        re.put("total",total);
        return AjaxResult.success(re);
    }


    @GetMapping("/answerliststatic")
    public AjaxResult answerliststatic(TYjyAnswer tYjyAnswer) {
        String workNo=SecurityUtils.getUsername();
        tYjyAnswer.setWorkNo(workNo);
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        List<JSONObject> result= new ArrayList<>();
        for(TYjyAnswer item:list)
        {
            JSONObject jsonResult=new JSONObject();
            JSONObject hisData=new JSONObject();
            JSONObject hisDatain=new JSONObject();
            jsonResult.put("formId",item.getFormId());
            jsonResult.put("creatorDept",item.getCreatorDept());
            TYjyForm form=tYjyFormService.selectTYjyFormById(item.getFormId());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("formType",form.getFormType());
            jsonResult.put("minimum",form.getMinimum());
            jsonResult.put("maximum",form.getMaximum());
//            jsonResult.put("formValue",item.getFormValue());
            if(item.getFormValue()==null)
            {
                hisDatain.put("formValue",null);
//                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                {
//                    hisDatain.put("formValue",new ArrayList());
//                }
//                else
//                {
//                    hisDatain.put("formValue",null);
//                }
            }
            else
            {
                if(form.getFormType().equals("0"))
                {
                    hisDatain.put("formValue",Integer.valueOf(item.getFormValue()));
                }
                if(form.getFormType().equals("1"))
                {
                    hisDatain.put("formValue",Double.valueOf(item.getFormValue()));
                }
                if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                {
                    hisDatain.put("formValue",item.getFormValue());
                }
                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
                {
                    hisDatain.put("formValue",JSONObject.parse(item.getFormValue()));
                }
            }
            item.setWorkNo(workNo);
            List<TYjyAnswer> answerlist =tYjyAnswerService.selectAnswerLast3(item);
            int count=0;
            while(count<answerlist.size())
            {
                hisDatain=new JSONObject();
                hisDatain.put("title",answerlist.get(count).getFcDate()+"("+answerlist.get(count).getCreatorName()+")");
                if(answerlist.get(count).getFormValue()==null)
                {
                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
                    {
                        hisDatain.put("value",new ArrayList<>());
                    }
                    else
                    {
                        hisDatain.put("value",null);
                    }
                }
                else
                {
                    if(form.getFormType().equals("0"))
                    {
                        hisDatain.put("value",Integer.valueOf(answerlist.get(count).getFormValue()));
                    }
                    if(form.getFormType().equals("1"))
                    {
                        hisDatain.put("value",Double.valueOf(answerlist.get(count).getFormValue()));
                    }
                    if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                    {
                        hisDatain.put("value",answerlist.get(count).getFormValue());
                    }
//                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                    {
//                        hisDatain.put("value",JSONObject.parse(answerlist.get(count).getFormValue()));
//                    }
                }

                hisData.put("data"+(count+1),hisDatain);
                jsonResult.put("hisData",hisData);
                count=count+1;
            }
            while(count<3)
            {
                hisDatain=new JSONObject();
                hisDatain.put("title",null);
                hisDatain.put("formValue",null);
//                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                {
//                    hisDatain.put("formValue",new ArrayList());
//                }
//                else
//                {
//                    hisDatain.put("formValue",null);
//                }
                hisData.put("data"+(count+1),hisDatain);
                count=count+1;
                jsonResult.put("hisData",hisData);
            }
            result.add(jsonResult);
        }
        return AjaxResult.success(result);
    }

//    @GetMapping("/listbyworkno")
//    public TableDataInfo listbyworkno(TYjyForm tYjyForm)
//    {
//        String workNo= SecurityUtils.getUsername();
//        tYjyForm.setWorkNo(workNo);
//        startPage();
//        List<TYjyForm> list = tYjyFormService.selectTYjyFormList(tYjyForm);
//        return getDataTable(list);
//    }

    /**
     * 导出TYjyForm列表
     */
    @Log(title = "TYjyForm", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TYjyForm tYjyForm) {
        List<TYjyForm> list = tYjyFormService.selectTYjyFormList(tYjyForm);
        ExcelUtil<TYjyForm> util = new ExcelUtil<TYjyForm>(TYjyForm.class);
        return util.exportExcel(list, "form");
    }

    /**
     * 获取TYjyForm详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(tYjyFormService.selectTYjyFormById(id));
    }

    /**
     * 新增TYjyForm
     */
    @Log(title = "TYjyForm", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TYjyForm tYjyForm) {
        TYjyForm count=new TYjyForm();
        List<TYjyForm> countlist;
        TYjyPermission insert;
        if(isexit(tYjyForm)>0)
        {
            return AjaxResult.error("在同一维度下，已存在相同名称的问题");
        }
        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyForm.getDimensionalityId());
        tYjyForm.setDimensionalityPath(tYjyDimensionality.getPath());
        tYjyForm.setDistributeDept(JSON.toJSONString(tYjyForm.getDeptCodes()));
        tYjyForm.setCheckerList(new ArrayList<>().toString());

        if(tYjyForm.getDeadlineSwitch().equals("1")&&tYjyForm.getMailSwitch().equals("1"))
        {
            tYjyForm.setMailDate(tYjyFormService.mailDateCount(tYjyForm));
        }
        tYjyFormService.insertTYjyForm(tYjyForm);
        count.setDimensionalityId(tYjyForm.getDimensionalityId());
        count.setFormQuestion(tYjyForm.getFormQuestion());
        countlist=tYjyFormService.selectTYjyFormList(count);

        for(String code:tYjyForm.getDeptCodes())
        {
            if(!code.trim().equals(""))
            {
                tYjyForm.setDeptShow(code);
                insert=new TYjyPermission();
                insert.setDeptCode(code);
                insert.setFormId(countlist.get(0).getId());
                insert.setRuleType("99");
                TYjyDeptUser administratorsearch=new TYjyDeptUser();
                administratorsearch.setPath(code);
                List<TYjyDeptUser> administrators =tYjyDeptUserService.selectAdministratorsfromroot(administratorsearch);
                if(administrators.size()==0)
                {
                    return error("该部门无分配人");
                }
                for(TYjyDeptUser item:administrators)
                {
                    insert.setWorkNo(item.getWorkNo());
                    insert.setRoleId(item.getId());
                    insert.setDeptName(item.getDeptName());
                    tYjyPermissionService.insertTYjyPermission(insert);
                }
//                List<String> administrators = tYjyDeptUserService.selectAdministratorsByCode(code);
//                if(administrators.size()==0)
//                {
//                    return error("该部门无分配人");
//                }
//                for(String item:administrators)
//                {
//                    insert.setWorkNo(item);
//                    tYjyPermissionService.insertTYjyPermission(insert);
//                }
            }
        }
        return toAjax(1);
    }

    //对指定问题进行全删全增权限修改
    @Log(title = "TYjyForm", businessType = BusinessType.INSERT)
    @PostMapping("/permissionChange")
    public AjaxResult TYjyPermissionchange(@RequestBody TYjyForm tYjyForm) {
        TYjyPermission insert;
        for(Long id:tYjyForm.getIds())
        {
            TYjyPermission tYjyPermission=new TYjyPermission();
            tYjyPermission.setFormId(id);
            tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
            for(String code:tYjyForm.getDeptCodes())
            {
                insert=new TYjyPermission();
                insert.setDeptCode(code);
                insert.setFormId(id);
                insert.setRuleType("99");
                List<String> administrators = tYjyDeptUserService.selectAdministratorsByCode(code);
                if(administrators.size()==0)
                {
                    return error("存在部门无管理员");
                }
                for(String item:administrators)
                {
                    insert.setWorkNo(item);
                    tYjyPermissionService.insertTYjyPermission(insert);
                }
            }
        }
        return toAjax(1);
    }

    /**
     * 修改TYjyForm
     */
    @Log(title = "TYjyForm", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TYjyForm tYjyForm) {

        TYjyForm re = tYjyFormService.selectTYjyFormById(tYjyForm.getId());
        if(!re.getFormQuestion().equals(tYjyForm.getFormQuestion()))
        {
            if(isexit(tYjyForm)>0)
            {
                return AjaxResult.error("在同一维度下，已存在相同名称的问题");
            }
        }
        TYjyPermission insert=new TYjyPermission();
        TYjyPermission tYjyPermission=new TYjyPermission();
        tYjyPermission.setFormId(tYjyForm.getId());
        tYjyForm.setDistributeDept(JSON.toJSONString(tYjyForm.getDeptCodes()));
        if(re.getDistributeDept().equals(tYjyForm.getDistributeDept()))
        {
            tYjyPermission.setRuleType("99");
            if(tYjyForm.getDeptCodes()!=null)
            {
                for(String code:tYjyForm.getDeptCodes())
                {
                    tYjyPermission.setDeptCode(code);
                    tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
                }
            }
        }
        else
        {
            tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
        }

        //在开关状态或者截止日期发生变化时触发邮件通知日期更新
        if((re.getDeadlineSwitch().equals("0")&&tYjyForm.getDeadlineSwitch().equals("1"))||(re.getDeadlineSwitch().equals("1")&&tYjyForm.getDeadlineSwitch().equals("1")&&(!re.getDeadlineDate().equals(tYjyForm.getDeadlineDate()))))
        {
            if((re.getMailSwitch().equals("0")&&tYjyForm.getMailSwitch().equals("1"))||(re.getMailSwitch().equals("1")&&tYjyForm.getMailSwitch().equals("1")&&(!re.getDeadlineDate().equals(tYjyForm.getDeadlineDate()))))
            {
                tYjyForm.setMailDate(tYjyFormService.mailDateCount(tYjyForm));
            }
        }
        else
        {
            if((re.getMailSwitch().equals("0")&&tYjyForm.getMailSwitch().equals("1"))||(re.getMailSwitch().equals("1")&&tYjyForm.getMailSwitch().equals("1")&&(!re.getDeadlineDate().equals(tYjyForm.getDeadlineDate()))))
            {
                tYjyForm.setMailDate(tYjyFormService.mailDateCount(tYjyForm));
            }
        }


        if(tYjyForm.getDeptCodes()!=null)
        {
            for(String code:tYjyForm.getDeptCodes())
            {
                tYjyForm.setDeptShow(code);
                insert=new TYjyPermission();
                insert.setDeptCode(code);
                insert.setFormId(tYjyForm.getId());
                insert.setRuleType("99");
                TYjyDeptUser administratorsearch=new TYjyDeptUser();
                administratorsearch.setPath(code);
                List<TYjyDeptUser> administrators =tYjyDeptUserService.selectAdministratorsfromroot(administratorsearch);
                if(administrators.size()==0)
                {
                    return error("该部门无分配人");
                }
                for(TYjyDeptUser item:administrators)
                {
                    insert.setWorkNo(item.getWorkNo());
                    insert.setRoleId(item.getId());
                    insert.setDeptName(item.getDeptName());
                    tYjyPermissionService.insertTYjyPermission(insert);
                }
            }
        }
        //现在只是存了基础的信息变化，但是没有存储对比和必要的其他内容
        TYjyFormHistory tYjyFormHistory=new TYjyFormHistory();
        tYjyFormHistory.setFormId(re.getId());
        tYjyFormHistory.setDimensionalityPath(re.getDimensionalityPath());
        tYjyFormHistory.setFormNote(re.getFormNote());
        tYjyFormHistory.setFormNote1(re.getFormNote1());
        tYjyFormHistory.setFormQuestion(re.getFormQuestion());
        tYjyFormHistory.setMaximum(re.getMaximum());
        tYjyFormHistory.setMinimum(re.getMinimum());
        tYjyFormHistory.setCreateBy(SecurityUtils.getUsername());
        tYjyFormHistory.setCreateTime(new Date());
        tYjyFormHistoryService.insertTYjyFormHistory(tYjyFormHistory);
//        if((re.getFormNote()!=null)&&(!re.getFormNote().equals(tYjyForm.getFormNote())))
//        {
//
//        }
//        if((re.getFormNote1()!=null)&&(!re.getFormNote1().equals(tYjyForm.getFormNote1())))
//        {
//
//        }
//        if((re.getFormNote1()!=null)&&(!re.getFormNote1().equals(tYjyForm.getFormNote1())))
//        {
//
//        }

        //取消此处同步更新，转而考虑保障指标修改后如何实现精准的校验，公式间存在的换算也让人无奈，数据会需要重新导入导出一份，功能真的是无休无止的，让人苦恼，让人无奈
//        if(re.getFormNote()!=null)
//        {
//            if(!re.getFormNote().equals(tYjyForm.getFormNote()))
//            {
//                tYjyAnswerService.editForm(tYjyForm);
//            }
//        }
//        if(re.getFormNote1()!=null)
//        {
//            if(!re.getFormNote1().equals(tYjyForm.getFormNote1()))
//            {
//                tYjyAnswerService.editForm(tYjyForm);
//            }
//        }
        return toAjax(tYjyFormService.updateTYjyForm(tYjyForm));
    }


    /**
     * 修改TYjyForm
     */
    @Log(title = "TYjyForm", businessType = BusinessType.UPDATE)
    @PutMapping("/deadlinebranch")
    public AjaxResult deadlinebranch(@RequestBody TYjyForm tYjyForm)
    {

        if(tYjyForm.getDimensionalityPath()==null)
        {
            TYjyDimensionality research= tYjyDimensionalityService.selectTYjyDimensionalityById(tYjyForm.getDimensionalityId());
            tYjyForm.setDimensionalityPath(research.getPath());
        }
        if(tYjyForm.getMailSwitch()==null)
        {
            tYjyForm.setMailSwitch("0");
        }
        //根据不同条件来决定是否需要批量修改细节，同时需要考虑正常情况下的细节
        if(tYjyForm.getDeadlineSwitch().equals("0")&&tYjyForm.getMailSwitch().equals("0"))
        {
            tYjyFormService.deadlineUpdate(tYjyForm);
        }
        if(tYjyForm.getDeadlineSwitch().equals("1")&&tYjyForm.getMailSwitch().equals("0"))
        {
            tYjyFormService.deadlineUpdate(tYjyForm);
        }
        if(tYjyForm.getDeadlineSwitch().equals("1")&&tYjyForm.getMailSwitch().equals("1"))
        {
            TYjyForm search= new TYjyForm();
            search.setDimensionalityPath(tYjyForm.getDimensionalityPath());
            List<TYjyForm> list=tYjyFormService.selectTYjyFormList(search);
            for(TYjyForm item:list)
            {
                item.setDeadlineSwitch(tYjyForm.getDeadlineSwitch());
                item.setMailSwitch(tYjyForm.getMailSwitch());
                item.setDeadlineDate(tYjyForm.getDeadlineDate());
                item.setCountdown(tYjyForm.getCountdown());
                item.setMailDate(tYjyFormService.mailDateCount(item));
                tYjyFormService.updateFormWithDeadline(item);
            }
        }
//        if(tYjyForm.getDeadlineSwitch().equals("0"))
//        {
//            tYjyFormService.deadlineUpdate(tYjyForm);
//        }
//        else
//        {
//            List<TYjyForm> list=tYjyFormService.selectTYjyFormList(tYjyForm);
//            for(TYjyForm item:list)
//            {
//                item.setDeadlineSwitch(tYjyForm.getDeadlineSwitch());
//                item.setMailDate(tYjyFormService.mailDateCount(item));
//                tYjyFormService.updateFormWithDeadline(item);
//            }
//        }
        return toAjax(1);
    }


    @Log(title = "TYjyForm", businessType = BusinessType.UPDATE)
    @PutMapping("/mailbranch")
    public AjaxResult mailbranch(@RequestBody TYjyForm tYjyForm)
    {
        if(tYjyForm.getDeadlineSwitch().equals("0"))
        {
            return toAjax(tYjyFormService.mailUpdate(tYjyForm));
        }
        else
        {
            List<TYjyForm> list=tYjyFormService.selectTYjyFormList(tYjyForm);
            for(TYjyForm item:list)
            {
                //此处更新有问题,需要重新考虑更新条件
                item.setMailSwitch(tYjyForm.getMailSwitch());
                item.setMailDate(tYjyFormService.mailDateCount(item));
                item.setCountdown(tYjyForm.getCountdown());
                tYjyFormService.updateFormWithMail(item);
            }
            return toAjax(1);
        }
        //此处更新有问题,需要重新考虑更新条件
    }
    



    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id)
    {
        TYjyPermission tYjyPermission=new TYjyPermission();
        tYjyPermission.setFormId(id);
        tYjyPermissionService.deleteTYjyPermissionByFormId(tYjyPermission);
        return toAjax(tYjyFormService.deleteTYjyFormById(id));
    }

    /**
     * 删除TYjyForm
     */
    @Log(title = "TYjyForm", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteAll/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tYjyFormService.deleteTYjyFormByIds(ids));
    }

    public int isexit(TYjyForm tYjyForm)
    {
        TYjyForm search=new TYjyForm();
        search.setDimensionalityId(tYjyForm.getDimensionalityId());
        search.setFormQuestion(tYjyForm.getFormQuestion());
        search.setDelFlag("0");
        return tYjyFormService.selectTYjyFormListWithFormQuestion(search).size();
    }



//    // 部门新增内容
//    @GetMapping("/listforadministrators")
//    public TableDataInfo listforadministrators(TYjyForm tYjyForm) {
//        tYjyForm.setWorkNo(SecurityUtils.getUsername());
//        startPage();
//        List<TYjyForm> list = tYjyFormService.selectForAdministrators(tYjyForm);
//        return getDataTable(list);
//    }



//    @Log(title = "TYjyForm更新权限", businessType = BusinessType.GRANT)
//    @PutMapping("/checkchange")
//    public AjaxResult checkchange(Long id,String deptCode, String[] workNos)
//    {
//        TYjyForm tYjyForm=tYjyFormService.selectTYjyFormById(id);
//        TYjyForm update=new TYjyForm();
//        update.setId(id);
//        update.setCheckerList(tYjyFormService.dealchecklist(tYjyForm.getCheckerList(),deptCode,workNos));
//        return toAjax(tYjyFormService.updateTYjyForm(update));
//    }



    @Log(title = "TYjyForm更新权限", businessType = BusinessType.GRANT)
    @PutMapping("/checkchange")
    public AjaxResult checkchange(@RequestBody TYjyForm tYjyForm)
    {
        TYjyForm update=new TYjyForm();
        update.setId(tYjyForm.getId());

        JSONArray re=null;
        if(tYjyForm.getCheckerList()!=null)
        {
            JSONArray list=JSONArray.parseArray(tYjyForm.getCheckerList());
            for(int i=0;i<list.size();i++)
            {
                JSONObject item =JSONObject.parseObject(list.get(i).toString());
                re=JSONArray.parseArray(item.get("examineList").toString());
            }
        }
        if(re.size()==0)
        {
            update.setCheckerList("[]");
        }
        else
        {
            update.setCheckerList(tYjyForm.getCheckerList());
        }

//        JSONArray checklist=tYjyFormService.getchecklist(search.getCheckerList(),search.getCreatorDept());


        return toAjax(tYjyFormService.updateTYjyForm(update));
    }


    @GetMapping("/addtest")
    public AjaxResult addtest(TYjyForm tYjyForm) {
        tYjyFormService.addAnswer();
        return success("1");
    }


    @GetMapping("/mailtest")
    public AjaxResult mailtest(TYjyForm tYjyForm) {
        tYjyFormService.mailtest();
        return success("1");
    }


    @PutMapping("/mailFordeal")
    public AjaxResult mailFordeal(@RequestBody TYjyForm tYjyForm) {
        tYjyFormService.mailFordeal(tYjyForm,tYjyForm.getFcDate());
        return success("1");
    }


    @GetMapping("/frequencyType")
    public AjaxResult frequencyType(TYjyForm tYjyForm) {
//        List<TYjyForm> searechList=tYjyFormService.selectTYjyFormList(tYjyForm);
//
//
//        int count=0;
//        for(TYjyForm item:searechList)
//        {
//            if(item.getFrequency().equals("0")||item.getFrequency().equals("5"))
//            {
//                count=1;
//                break;
//            }
//        }
//        return success(count);

        Integer num=tYjyFormService.selectFrequencyCount(tYjyForm);
        int count=0;
        if(num>0)
        {
            count=1;
        }
        return success(count);
    }


}
