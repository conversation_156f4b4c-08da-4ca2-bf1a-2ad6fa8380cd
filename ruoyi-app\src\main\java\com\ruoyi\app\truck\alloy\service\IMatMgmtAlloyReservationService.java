package com.ruoyi.app.truck.alloy.service;

import com.ruoyi.app.truck.alloy.domain.MatMgmtAlloyReservation;
import com.ruoyi.app.truck.alloy.dto.AlloyVerifyDTO;
import com.ruoyi.app.truck.alloy.dto.AlloyAssignDTO;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 物管合金车辆预约Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IMatMgmtAlloyReservationService {
    /**
     * 查询物管合金车辆预约详情
     *
     * @param reservationNo 物管合金车辆预约编号
     * @return 物管合金车辆预约
     */
    public MatMgmtAlloyReservation detail(String reservationNo);

    /**
     * 查询物管合金车辆预约
     *
     * @param id 物管合金车辆预约ID
     * @return 物管合金车辆预约
     */
    public MatMgmtAlloyReservation selectMatMgmtAlloyReservationById(Long id);

    /**
     * 查询物管合金车辆预约列表
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 物管合金车辆预约集合
     */
    public List<MatMgmtAlloyReservation> selectMatMgmtAlloyReservationList(MatMgmtAlloyReservation matMgmtAlloyReservation);

    /**
     * 新增物管合金车辆预约
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    public int insertMatMgmtAlloyReservation(MatMgmtAlloyReservation matMgmtAlloyReservation);

    /**
     * 修改物管合金车辆预约
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    public int updateMatMgmtAlloyReservation(MatMgmtAlloyReservation matMgmtAlloyReservation);


    /**
     * 修改物管合金车辆预约
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return 结果
     */
    public int updateMatMgmtAlloyReservationStatus(MatMgmtAlloyReservation matMgmtAlloyReservation);

    /**
     * 批量删除物管合金车辆预约
     *
     * @param ids 需要删除的物管合金车辆预约ID
     * @return 结果
     */
    public int deleteMatMgmtAlloyReservationByIds(Long[] ids);

    /**
     * 删除物管合金车辆预约信息
     *
     * @param id 物管合金车辆预约ID
     * @return 结果
     */
    public int deleteMatMgmtAlloyReservationById(Long id);

    /**
     * 新增合金预约（业务方法）
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @param openId                  用户OpenId
     * @return 结果
     */
    public int insertMatMgmtAlloyReservationBusiness(MatMgmtAlloyReservation matMgmtAlloyReservation, String openId);

    /**
     * 判断用户是否具有合金管理角色
     *
     * @param workNo 工号
     * @return 是否具有管理角色
     */
    public boolean hasAlloyAdminRole(String workNo);

    /**
     * 查询物管合金车辆预约列表（返回TableDataInfo）
     *
     * @param matMgmtAlloyReservation 物管合金车辆预约
     * @return TableDataInfo
     */
    public TableDataInfo selectMatMgmtAlloyReservationTableList(MatMgmtAlloyReservation matMgmtAlloyReservation);

    /**
     * 获取预约详情
     *
     * @param reservationNo 预约编号
     * @param workNo        工号
     * @return 预约详情Map
     */
    public Map<String, Object> getReservationDetail(String reservationNo, String workNo);

    /**
     * 审核预约
     *
     * @param verifyDTO 审核DTO
     * @return 结果
     */
    public int approveReservation(AlloyVerifyDTO verifyDTO);

    /**
     * 取消申请
     *
     * @param reservationNo 预约编号
     * @param openId        用户OpenId
     * @return 结果
     */
    public int cancelReservation(String reservationNo, String openId);

    /**
     * 合金扫码操作（直接修改、暂不使用）
     *
     * @param reservationNo 预约编号
     * @param scanAction    扫码动作（checkIn-签到，entranceGuard-门卫扫码）
     * @param workNo        操作人工号
     * @param workName      操作人姓名
     * @return 结果Map
     */
    public Map<String, Object> alloyScanCode(String reservationNo, String scanAction, String workNo, String workName);

    /**
     * 物管分配
     *
     * @param assignDTO 分配参数DTO
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果
     */
    public int materialAssign(AlloyAssignDTO assignDTO, String workNo, String workName);

    /**
     * 重新分配
     *
     * @param assignDTO 分配参数DTO
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果
     */
    public int materialReassign(AlloyAssignDTO assignDTO, String workNo, String workName);

    /**
     * 物管确认
     *
     * @param reservationNo 预约编号
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果
     */
    public int materialConfirm(String reservationNo, String workNo, String workName);

    /**
     * 签到
     *
     * @param reservationNo 预约编号
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果
     */
    public int checkIn(String reservationNo, String workNo, String workName);

    /**
     * 门卫确认通行（安全村）
     *
     * @param reservationNo 预约编号
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果
     */
    public int entranceGuardScan(String reservationNo, String workNo, String workName);

    /**
     * 扫码检查（只检查状态，不修改数据）
     *
     * @param reservationNo 预约编号
     * @param scanAction 扫码动作（checkIn-签到，entranceGuard-门卫扫码，matConfirm-物管确认）
     * @param workNo 操作人工号
     * @param workName 操作人姓名
     * @return 结果Map
     */
    public Map<String, Object> scanCheck(String reservationNo, String scanAction, String workNo, String workName);

}
